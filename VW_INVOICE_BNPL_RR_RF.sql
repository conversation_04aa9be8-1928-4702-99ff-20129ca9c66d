-- ONEDATA.VW_INVOICE_BNPL_RR_RF source

CREATE OR REPLACE FORCE EDITIONABLE VIEW "ONEDATA"."VW_INVOICE_BNPL_RR_RF" ("S_INVOICE_ID", "<PERSON>_<PERSON>E<PERSON>", "D_UPDATE_INVOICE", "N_ORDER_TXN_ID", "S_TRANS_TYPE", "S_APP_NAME", "S_TRANSACTIONID", "D_DATE", "S_MERCHANTID", "S_INVOICE_MERCHANTID", "S_ORDERREFERENCE", "S_ORDERID", "S_ME<PERSON><PERSON>NT<PERSON>ANSACTIONREFEREN", "S_TRANSACTIONTYPE", "S_ACQUIRERID", "S_BATCHNUMBER", "S_CURRENCY", "N_AMOUNT", "S_RRN", "S_RESPONSE_CODE", "S_ACQUIRERRESPONSECO<PERSON>", "S_OPERATORID", "S_MERCHANT<PERSON>ANSACTIONSOURCE", "S_ORDERDAT<PERSON>", "S_CARDNO", "S_CARD_EXP", "S_FRAUD", "S_FRAUD_DESCRIPTION", "S_FRAUDDATE", "S_AUTHORISED_AMOUNT", "S_CAPTURED_AMOUNT", "S_REFUNDED_AMOUNT", "S_PAYMENT_METHOD", "S_INTEGRATION_TYPE", "S_INTEGRATION_TYPE_VERSION", "S_EMAIL_MERCH", "S_EMAIL_CUSTOMER", "S_EMAIL_FRAUD", "D_SETTLEMENT", "N_VERSION", "S_PAYGATE", "S_STATUS", "S_REASON_NAME", "S_DATA", "S_VOID_DESC", "D_UPDATE", "D_UPDATE3", "S_CUSTOMER_NAME", "S_ORDER_NOTE", "S_CUSTOMER_EMAIL", "S_USER_ID") DEFAULT COLLATION "USING_NLS_COMP"  AS 
  SELECT
        "S_INVOICE_ID",
        "S_KEY",
        "D_UPDATE_INVOICE",
        "N_ORDER_TXN_ID",
        "S_TRANS_TYPE",
        "S_APP_NAME",
        "S_TRANSACTIONID",
        "D_DATE",
        "S_MERCHANTID",
        "S_INVOICE_MERCHANTID",
        "S_ORDERREFERENCE",
        "S_ORDERID",
        "S_MERCHANTTRANSACTIONREFEREN",
        "S_TRANSACTIONTYPE",
        "S_ACQUIRERID",
        "S_BATCHNUMBER",
        "S_CURRENCY",
        "N_AMOUNT",
        "S_RRN",
        "S_RESPONSE_CODE",
        "S_ACQUIRERRESPONSECODE",
        "S_OPERATORID",
        "S_MERCHANTTRANSACTIONSOURCE",
        "S_ORDERDATE",
        "S_CARDNO",
        "S_CARD_EXP",
        "S_FRAUD",
        "S_FRAUD_DESCRIPTION",
        "S_FRAUDDATE",
        "S_AUTHORISED_AMOUNT",
        "S_CAPTURED_AMOUNT",
        "S_REFUNDED_AMOUNT",
        "S_PAYMENT_METHOD",
        "S_INTEGRATION_TYPE",
        "S_INTEGRATION_TYPE_VERSION",
        "S_EMAIL_MERCH",
        "S_EMAIL_CUSTOMER",
        "S_EMAIL_FRAUD",
        "D_SETTLEMENT",
        "N_VERSION",
        "S_PAYGATE",
        "S_STATUS",
        "S_REASON_NAME",
        "S_DATA",
        "S_VOID_DESC",
        "D_UPDATE",
        "D_UPDATE3",
        "S_CUSTOMER_NAME",
        "S_ORDER_NOTE",
        "S_CUSTOMER_EMAIL",
        "S_USER_ID"
    FROM
        (
            SELECT
                invoice.s_id                                                         s_invoice_id,
                to_char(rr.n_id)                                                     s_key,
                invoice.d_update                                                     d_update_invoice,
                tbot.n_transaction_id                                                n_order_txn_id,
                'BNPL'                                                                 s_trans_type,
                invoice_convert_msp_app(payment.s_client_id, payment.s_ins_brand_id) s_app_name,
                to_char(rr.n_id)                                                     s_transactionid,
                rr.d_create                                                          d_date,
                rr.s_merchant_id                                                     s_merchantid,
                orders.s_merchant_id                                                 s_invoice_merchantid,
                orders.s_order_info                                                  s_orderreference,
                to_char(orders.n_ordered_id)                                         s_orderid,
                tbot.n_transaction_id                                                s_merchanttransactionreferen,
                'Request Refund'                                                     s_transactiontype,
                nvl(regexp_substr(rr.s_data, '"acquirer_id"\s*:\s*"([^"]+)"', 1, 1, NULL,
                                  1),
                    '')                                                              s_acquirerid,
                ''                                                                   s_batchnumber,
                payment.s_currency                                                   s_currency,
                ( rr.n_amount * 100 )                                                n_amount,
                ''                                                                   s_rrn,
                invoice_convert_refund_request(rr.s_state, rr.s_process_state, 'QT') s_response_code,
                ''                                                                   s_acquirerresponsecode,
                regexp_substr(rr.s_refund_request, '"vpc_Operator"\s*:\s*"([^"]+)"', 1, 1, NULL,
                              1)                                                     s_operatorid,
                ''                                                                   s_merchanttransactionsource,
                ''                                                                   s_orderdate,
                payment.s_ins_number                                                 s_cardno,
                payment.s_ins_month
                || '/'
                || payment.s_ins_year                                                s_card_exp,
                ''                                                                   s_fraud,
                ''                                                                   s_fraud_description,
                ''                                                                   s_frauddate,
                ''                                                                   s_authorised_amount,
                ''                                                                   s_captured_amount,
                ''                                                                   s_refunded_amount,
                ''                                                                   s_payment_method,
                ''                                                                   s_integration_type,
                ''                                                                   s_integration_type_version,
                ''                                                                   s_email_merch,
                ''                                                                   s_email_customer,
                ''                                                                   s_email_fraud,
                rr.d_create                                                          d_settlement,
                2                                                                    n_version,
                'BNPL'                                                                 s_paygate,
                ''                                                                   s_status,
                ''                                                                   s_reason_name,
                payment.s_data                                                       s_data,
                ''                                                                   s_void_desc,
                rr.d_update                                                          d_update,
                rr.d_update                                                          d_update3,
                orders.s_customer_name                                               s_customer_name,
                orders.s_order_note                                                  s_order_note,
                orders.s_customer_email                                              s_customer_email,
                orders.s_user_id                                                     s_user_id
            FROM
                     tb_invoice_order orders
                INNER JOIN tb_invoice_order_trans tbot ON  orders.n_ordered_id = tbot.n_order_id
                INNER JOIN tb_msp_invoice         invoice ON ( to_char(tbot.n_transaction_id) = invoice.s_merch_invoice_ref
                                                       AND tbot.s_merchant_id = invoice.s_merchant_id )
                INNER JOIN tb_msp_payment         payment ON payment.s_invoice_id = invoice.s_id
                INNER JOIN tb_msp_refund_request  rr ON payment.s_id = rr.s_payment_id
            WHERE
                payment.s_ins_type = 'bnpl'
                AND rr.d_update > sysdate - INTERVAL '1' DAY
            UNION ALL
            SELECT
                invoice.s_id                                 s_invoice_id,
                refund.s_id                                  s_key,
                invoice.d_update                             d_update_invoice,
                tbot.n_transaction_id                        n_order_txn_id,
                'BNPL'                                         s_trans_type,
                invoice_convert_msp_app(payment.s_client_id, payment.s_ins_brand_id) s_app_name,
                refund.s_id                                  s_transactionid,
                refund.d_create                              d_date,
                refund.s_merchant_id                         s_merchantid,
                orders.s_merchant_id                         s_invoice_merchantid,
                orders.s_order_info                          s_orderreference,
                to_char(orders.n_ordered_id)                 s_orderid,
                tbot.n_transaction_id                        s_merchanttransactionreferen,
                'Refund'                                     s_transactiontype,
                nvl(regexp_substr(refund.s_description, '"acquirer_id"\s*:\s*"([^"]+)"', 1, 1, NULL,
                                  1),
                    '')                                      s_acquirerid,
                ''                                           s_batchnumber,
                payment.s_currency                           s_currency,
                ( refund.n_amount * 100 )                    n_amount,
                ''                                           s_rrn,
                invoice_convert_refund(refund.s_state, 'QT') s_response_code,
                ''                                           s_acquirerresponsecode,
                regexp_substr(refund.s_data, '"operator"\s*:\s*"([^"]+)"', 1, 1, NULL,
                              1)                             s_operatorid,
                ''                                           s_merchanttransactionsource,
                ''                                           s_orderdate,
                payment.s_ins_number                         s_cardno,
                payment.s_ins_month
                || '/'
                || payment.s_ins_year                        s_card_exp,
                ''                                           s_fraud,
                ''                                           s_fraud_description,
                ''                                           s_frauddate,
                ''                                           s_authorised_amount,
                ''                                           s_captured_amount,
                ''                                           s_refunded_amount,
                ''                                           s_payment_method,
                ''                                           s_integration_type,
                ''                                           s_integration_type_version,
                ''                                           s_email_merch,
                ''                                           s_email_customer,
                ''                                           s_email_fraud,
                nvl(refund.d_settlement, '')                 d_settlement,
                2                                            n_version,
                'BNPL'                                       s_paygate,
                ''                                           s_status,
                ''                                           s_reason_name,
                payment.s_data                               s_data,
                ''                                           s_void_desc,
                refund.d_update                              d_update,
                refund.d_update                              d_update3,
                orders.s_customer_name                       s_customer_name,
                orders.s_order_note                          s_order_note,
                orders.s_customer_email                      s_customer_email,
                orders.s_user_id                             s_user_id
            FROM
                     tb_invoice_order orders
                INNER JOIN tb_invoice_order_trans tbot ON  orders.n_ordered_id = tbot.n_order_id 
                INNER JOIN tb_msp_invoice         invoice ON ( to_char(tbot.n_transaction_id) = invoice.s_merch_invoice_ref
                                                       AND tbot.s_merchant_id = invoice.s_merchant_id )
                INNER JOIN tb_msp_payment         payment ON payment.s_invoice_id = invoice.s_id
                INNER JOIN tb_msp_refund          refund ON refund.s_payment_id = payment.s_id
            WHERE
                 payment.s_ins_type = 'bnpl'
                AND refund.d_update > sysdate - INTERVAL '1' DAY
        );