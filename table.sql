CREATE TABLE
    "ONEREPORT"."TB_RP_ONEINVOICE" (
        "S_EMAIL_CUSTOMER" VARCHAR2 (1000) COLLATE "USING_NLS_COMP",
        "S_EMAIL_FRAUD" VARCHAR2 (1000) COLLATE "USING_NLS_COMP",
        "S_REMOTE_ADD" VARCHAR2 (1000) COLLATE "USING_NLS_COMP",
        "S_LOCALE" VARCHAR2 (1000) COLLATE "USING_NLS_COMP",
        "D_SETTLEMENT" DATE,
        "S_CARD_LEVEL_INDICATOR" VARCHAR2 (1000) COLLATE "USING_NLS_COMP",
        "S_COMMERCIAL_CARD" VARCHAR2 (1000) COLLATE "USING_NLS_COMP",
        "S_COMMERCIAL_CARD_INDICATOR" VARCHAR2 (1000) COLLATE "USING_NLS_COMP",
        "S_RISK_OVERALL_RESULT" VARCHAR2 (1000) COLLATE "USING_NLS_COMP",
        "S_TXN_REVERSAL_RESULT" VARCHAR2 (1000) COLLATE "USING_NLS_COMP",
        "N_VERSION" NUMBER,
        "S_PAYGATE" VARCHAR2 (1000) COLLATE "USING_NLS_COMP",
        "S_STATUS" VARCHAR2 (1000) COLLATE "USING_NLS_COMP",
        "S_REASON_NAME" VARCHAR2 (1000) COLLATE "USING_NLS_COMP",
        "S_DATA" VARCHAR2 (4000) COLLATE "USING_NLS_COMP",
        "S_VOID_DESC" VARCHAR2 (1000) COLLATE "USING_NLS_COMP",
        "D_UPDATE" DATE,
        "S_TRANS_TYPE" VARCHAR2 (1000) COLLATE "USING_NLS_COMP",
        "S_APP_NAME" VARCHAR2 (1000) COLLATE "USING_NLS_COMP",
        "S_TRANSACTIONID" VARCHAR2 (1000) COLLATE "USING_NLS_COMP",
        "D_DATE" DATE,
        "S_MERCHANTID" VARCHAR2 (1000) COLLATE "USING_NLS_COMP",
        "S_INVOICE_MERCHANTID" VARCHAR2 (1000) COLLATE "USING_NLS_COMP",
        "S_ORDERREFERENCE" VARCHAR2 (1000) COLLATE "USING_NLS_COMP",
        "S_ORDERID" VARCHAR2 (1000) COLLATE "USING_NLS_COMP",
        "S_MERCHANTTRANSACTIONREFEREN" VARCHAR2 (1000) COLLATE "USING_NLS_COMP",
        "S_TRANSACTIONTYPE" VARCHAR2 (1000) COLLATE "USING_NLS_COMP",
        "S_ACQUIRERID" VARCHAR2 (1000) COLLATE "USING_NLS_COMP",
        "S_BATCHNUMBER" VARCHAR2 (1000) COLLATE "USING_NLS_COMP",
        "S_CURRENCY" VARCHAR2 (1000) COLLATE "USING_NLS_COMP",
        "N_AMOUNT" NUMBER,
        "S_RRN" VARCHAR2 (1000) COLLATE "USING_NLS_COMP",
        "S_RESPONSE_CODE" VARCHAR2 (1000) COLLATE "USING_NLS_COMP",
        "S_ACQUIRERRESPONSECODE" VARCHAR2 (1000) COLLATE "USING_NLS_COMP",
        "S_AUTHORISATION_CODE" VARCHAR2 (1000) COLLATE "USING_NLS_COMP",
        "S_OPERATORID" VARCHAR2 (1000) COLLATE "USING_NLS_COMP",
        "S_MERCHANTTRANSACTIONSOURCE" VARCHAR2 (1000) COLLATE "USING_NLS_COMP",
        "S_ORDERDATE" VARCHAR2 (1000) COLLATE "USING_NLS_COMP",
        "S_CARD_TYPE" VARCHAR2 (1000) COLLATE "USING_NLS_COMP",
        "S_CARDNO" VARCHAR2 (1000) COLLATE "USING_NLS_COMP",
        "S_CARD_EXP" VARCHAR2 (1000) COLLATE "USING_NLS_COMP",
        "S_CSCRESULT_CODE" VARCHAR2 (1000) COLLATE "USING_NLS_COMP",
        "S_FRAUD" VARCHAR2 (1000) COLLATE "USING_NLS_COMP",
        "S_FRAUD_DESCRIPTION" VARCHAR2 (1000) COLLATE "USING_NLS_COMP",
        "S_FRAUDDATE" VARCHAR2 (1000) COLLATE "USING_NLS_COMP",
        "S_TICKET_NUMBER" VARCHAR2 (1000) COLLATE "USING_NLS_COMP",
        "S_AUTHORISED_AMOUNT" VARCHAR2 (1000) COLLATE "USING_NLS_COMP",
        "S_CAPTURED_AMOUNT" VARCHAR2 (1000) COLLATE "USING_NLS_COMP",
        "S_REFUNDED_AMOUNT" VARCHAR2 (1000) COLLATE "USING_NLS_COMP",
        "S_ADDRESS" VARCHAR2 (1000) COLLATE "USING_NLS_COMP",
        "S_CITY_TOWN" VARCHAR2 (1000) COLLATE "USING_NLS_COMP",
        "S_STATE_PROVINCE" VARCHAR2 (1000) COLLATE "USING_NLS_COMP",
        "S_ZIP_POSTAL_CODE" VARCHAR2 (1000) COLLATE "USING_NLS_COMP",
        "S_COUNTRY" VARCHAR2 (1000) COLLATE "USING_NLS_COMP",
        "S_AVS_RESULT_CODE" VARCHAR2 (1000) COLLATE "USING_NLS_COMP",
        "S_PAYMENT_METHOD" VARCHAR2 (1000) COLLATE "USING_NLS_COMP",
        "S_INTEGRATION_TYPE" VARCHAR2 (1000) COLLATE "USING_NLS_COMP",
        "S_INTEGRATION_TYPE_VERSION" VARCHAR2 (1000) COLLATE "USING_NLS_COMP",
        "S_PAYMENT_AUTHENTICATION_ID" VARCHAR2 (1000) COLLATE "USING_NLS_COMP",
        "S_AUTHENTICATION_TYPE" VARCHAR2 (1000) COLLATE "USING_NLS_COMP",
        "S_AUTHENTICATION_STATE" VARCHAR2 (1000) COLLATE "USING_NLS_COMP",
        "S_VERIFICATION_SECURITY_LEVEL" VARCHAR2 (1000) COLLATE "USING_NLS_COMP",
        "S_EMAIL_MERCH" VARCHAR2 (1000) COLLATE "USING_NLS_COMP",
        "D_UPDATE1" DATE,
        "D_UPDATE2" DATE,
        "S_ORDER_TXN_ID" VARCHAR2 (1000) COLLATE "USING_NLS_COMP",
        "S_CUSTOMER_NAME" VARCHAR2 (1000) COLLATE "USING_NLS_COMP",
        "S_CUSTOMER_EMAIL" VARCHAR2 (1000) COLLATE "USING_NLS_COMP",
        "S_ORDER_NOTE" VARCHAR2 (4000) COLLATE "USING_NLS_COMP",
        "S_USER_NAME" VARCHAR2 (1000) COLLATE "USING_NLS_COMP",
        "D_UPDATE3" DATE,
        "D_UPDATE4" DATE,
        "S_KEY" VARCHAR2 (200) COLLATE "USING_NLS_COMP" NOT NULL ENABLE,
        "S_INVOICE_ID" VARCHAR2 (200) COLLATE "USING_NLS_COMP",
        "D_UPDATE_INVOICE" DATE,
        "S_USER_ID" VARCHAR2 (255) COLLATE "USING_NLS_COMP",
        "S_NAME_ON_CARD" VARCHAR2 (100) COLLATE "USING_NLS_COMP",
        "D_UPDATE6" DATE,
        "S_ORG_MERCH_REF" VARCHAR2 (64) COLLATE "USING_NLS_COMP",
        "D_UPDATE7" DATE,
        "D_UPDATE8" DATE,
        "D_UPDATE9" DATE,
        CONSTRAINT "TB_RP_ONEINVOICE_PK" PRIMARY KEY ("S_KEY") USING INDEX PCTFREE 10 INITRANS 2 MAXTRANS 255 COMPUTE STATISTICS STORAGE (
            INITIAL 65536 NEXT 1048576 MINEXTENTS 1 MAXEXTENTS 2147483645 PCTINCREASE 0 FREELISTS 1 FREELIST GROUPS 1 BUFFER_POOL DEFAULT FLASH_CACHE DEFAULT CELL_FLASH_CACHE DEFAULT
        ) TABLESPACE "ONEREPORT" ENABLE
    ) DEFAULT COLLATION "USING_NLS_COMP" SEGMENT CREATION IMMEDIATE PCTFREE 10 PCTUSED 40 INITRANS 1 MAXTRANS 255 NOCOMPRESS LOGGING STORAGE (
        INITIAL 65536 NEXT 1048576 MINEXTENTS 1 MAXEXTENTS 2147483645 PCTINCREASE 0 FREELISTS 1 FREELIST GROUPS 1 BUFFER_POOL DEFAULT FLASH_CACHE DEFAULT CELL_FLASH_CACHE DEFAULT
    ) TABLESPACE "ONEREPORT";

-- "S_EMAIL_CUSTOMER"
-- "S_EMAIL_FRAUD"
-- "S_REMOTE_ADD"
-- "S_LOCALE"
-- "D_SETTLEMENT"
-- "S_CARD_LEVEL_INDICATOR"
-- "S_COMMERCIAL_CARD"
-- "S_COMMERCIAL_CARD_INDICATOR"
-- "S_RISK_OVERALL_RESULT"
-- "S_TXN_REVERSAL_RESULT"
-- "N_VERSION"
-- "S_PAYGATE"
-- "S_STATUS"
-- "S_REASON_NAME"
-- "S_DATA"
-- "S_VOID_DESC"
-- "D_UPDATE"
-- "S_TRANS_TYPE"
-- "S_APP_NAME"
-- "S_TRANSACTIONID"
-- "D_DATE"
-- "S_MERCHANTID"
-- "S_INVOICE_MERCHANTID"
-- "S_ORDERREFERENCE"
-- "S_ORDERID"
-- "S_MERCHANTTRANSACTIONREFEREN"
-- "S_TRANSACTIONTYPE"
-- "S_ACQUIRERID"
-- "S_BATCHNUMBER"
-- "S_CURRENCY"
-- "N_AMOUNT"
-- "S_RRN"
-- "S_RESPONSE_CODE"
-- "S_ACQUIRERRESPONSECODE"
-- "S_AUTHORISATION_CODE"
-- "S_OPERATORID"
-- "S_MERCHANTTRANSACTIONSOURCE"
-- "S_ORDERDATE"
-- "S_CARD_TYPE"
-- "S_CARDNO"
-- "S_CARD_EXP"
-- "S_CSCRESULT_CODE"
-- "S_FRAUD"
-- "S_FRAUD_DESCRIPTION"
-- "S_FRAUDDATE"
-- "S_TICKET_NUMBER"
-- "S_AUTHORISED_AMOUNT"
-- "S_CAPTURED_AMOUNT"
-- "S_REFUNDED_AMOUNT"
-- "S_ADDRESS"
-- "S_CITY_TOWN"
-- "S_STATE_PROVINCE"
-- "S_ZIP_POSTAL_CODE"
-- "S_COUNTRY"
-- "S_AVS_RESULT_CODE"
-- "S_PAYMENT_METHOD"
-- "S_INTEGRATION_TYPE"
-- "S_INTEGRATION_TYPE_VERSION"
-- "S_PAYMENT_AUTHENTICATION_ID"
-- "S_AUTHENTICATION_TYPE"
-- "S_AUTHENTICATION_STATE"
-- "S_VERIFICATION_SECURITY_LEVEL"
-- "S_EMAIL_MERCH"
-- "D_UPDATE1"
-- "D_UPDATE2"
-- "S_ORDER_TXN_ID"
-- "S_CUSTOMER_NAME"
-- "S_CUSTOMER_EMAIL"
-- "S_ORDER_NOTE"
-- "S_USER_NAME"
-- "D_UPDATE3"
-- "D_UPDATE4"
-- "S_KEY"
-- "S_INVOICE_ID"
-- "D_UPDATE_INVOICE"
-- "S_USER_ID"
-- "S_NAME_ON_CARD"
-- "D_UPDATE6"
-- "S_ORG_MERCH_REF"
-- "D_UPDATE7"
-- "D_UPDATE8"
-- "D_UPDATE9"


-- s_email_customer
-- s_email_fraud
-- s_remote_add
-- s_locale
-- d_settlement
-- s_card_level_indicator
-- s_commercial_card
-- s_commercial_card_indicator
-- s_risk_overall_result
-- s_txn_reversal_result
-- n_version
-- s_paygate
-- s_status
-- s_reason_name
-- s_data
-- s_void_desc
-- d_update
-- s_trans_type
-- s_app_name
-- s_transactionid
-- d_date
-- s_merchantid
-- s_invoice_merchantid
-- s_orderreference
-- s_orderid
-- s_merchanttransactionreferen
-- s_transactiontype
-- s_acquirerid
-- s_batchnumber
-- s_currency
-- n_amount
-- s_rrn
-- s_response_code
-- s_acquirerresponsecode
-- s_authorisation_code
-- s_operatorid
-- s_merchanttransactionsource
-- s_orderdate
-- s_card_type
-- s_cardno
-- s_card_exp
-- s_cscresult_code
-- s_fraud
-- s_fraud_description
-- s_frauddate
-- s_ticket_number
-- s_authorised_amount
-- s_captured_amount
-- s_refunded_amount
-- s_address
-- s_city_town
-- s_state_province
-- s_zip_postal_code
-- s_country
-- s_avs_result_code
-- s_payment_method
-- s_integration_type
-- s_integration_type_version
-- s_payment_authentication_id
-- s_authentication_type
-- s_authentication_state
-- s_verification_security_level
-- s_email_merch
-- d_update1
-- d_update2
-- s_order_txn_id
-- s_customer_name
-- s_customer_email
-- s_order_note
-- s_user_name
-- d_update3
-- d_update4
-- s_key
-- s_invoice_id
-- d_update_invoice
-- s_user_id
-- s_name_on_card
-- d_update6
-- s_org_merch_ref
-- d_update7
-- d_update8
-- d_update9