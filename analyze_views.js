const fs = require('fs');
const path = require('path');

// Đọc file job.json
function readJobConfig() {
    try {
        const jobData = fs.readFileSync('job.json', 'utf8');
        return JSON.parse(jobData);
    } catch (error) {
        console.error('Lỗi khi đọc file job.json:', error);
        return null;
    }
}

// Lấy danh sách tất cả file SQL (trừ table.sql)
function getSqlFiles() {
    const files = fs.readdirSync('.')
        .filter(file => file.endsWith('.sql') && file !== 'table.sql')
        .sort();
    return files;
}

// Trích xuất tên các field từ CREATE VIEW statement
function extractFieldsFromSql(sqlContent) {
    const fields = [];
    
    // Tìm phần CREATE VIEW với danh sách fields trong dấu ngoặc
    const createViewMatch = sqlContent.match(/CREATE\s+OR\s+REPLACE.*?VIEW.*?\((.*?)\)/is);
    
    if (createViewMatch) {
        const fieldsSection = createViewMatch[1];
        // Tách các field bằng dấu phẩy, loại bỏ quotes và khoảng trắng
        const fieldMatches = fieldsSection.match(/"([^"]+)"/g);
        if (fieldMatches) {
            fieldMatches.forEach(match => {
                const fieldName = match.replace(/"/g, '');
                fields.push(fieldName);
            });
        }
    }
    
    return fields;
}

// Lấy ext_field_map từ job.json cho một view cụ thể
function getExtFieldMapForView(jobConfig, viewName) {
    if (!jobConfig || !jobConfig.configs || !jobConfig.configs[0] || !jobConfig.configs[0].list) {
        return {};
    }
    
    const viewConfig = jobConfig.configs[0].list.find(item => item.from_table === viewName);
    
    if (viewConfig && viewConfig.ext_field_map) {
        try {
            return JSON.parse(viewConfig.ext_field_map);
        } catch (error) {
            console.error(`Lỗi khi parse ext_field_map cho view ${viewName}:`, error);
            return {};
        }
    }
    
    return {};
}

// Tạo CSV từ dữ liệu
function createCsv(viewsData, allFields) {
    let csv = 'Field';
    
    // Thêm header cho các view
    Object.keys(viewsData).forEach(viewName => {
        csv += `,${viewName}`;
    });
    csv += '\n';
    
    // Thêm dữ liệu cho từng field
    allFields.forEach(field => {
        csv += `"${field}"`;
        
        Object.keys(viewsData).forEach(viewName => {
            const viewData = viewsData[viewName];
            let cellValue = '';
            
            if (viewData.fields.includes(field)) {
                cellValue = 'Có trong view';
            } else if (viewData.extFieldMap[field]) {
                cellValue = viewData.extFieldMap[field];
            } else {
                cellValue = 'Không có';
            }
            
            csv += `,"${cellValue}"`;
        });
        
        csv += '\n';
    });
    
    return csv;
}

// Hàm chính
function main() {
    console.log('Bắt đầu phân tích các file SQL...');
    
    // Đọc cấu hình job
    const jobConfig = readJobConfig();
    if (!jobConfig) {
        console.error('Không thể đọc file job.json');
        return;
    }
    
    // Lấy danh sách file SQL
    const sqlFiles = getSqlFiles();
    console.log(`Tìm thấy ${sqlFiles.length} file SQL:`, sqlFiles);
    
    const viewsData = {};
    const allFieldsSet = new Set();
    
    // Xử lý từng file SQL
    sqlFiles.forEach(sqlFile => {
        console.log(`Đang xử lý file: ${sqlFile}`);
        
        try {
            const sqlContent = fs.readFileSync(sqlFile, 'utf8');
            const viewName = path.basename(sqlFile, '.sql');
            
            // Trích xuất fields từ SQL
            const fields = extractFieldsFromSql(sqlContent);
            console.log(`  - Tìm thấy ${fields.length} fields trong ${viewName}`);
            
            // Lấy ext_field_map từ job.json
            const extFieldMap = getExtFieldMapForView(jobConfig, viewName);
            console.log(`  - Tìm thấy ${Object.keys(extFieldMap).length} ext_field_map entries cho ${viewName}`);
            
            // Lưu dữ liệu view
            viewsData[viewName] = {
                fields: fields,
                extFieldMap: extFieldMap
            };
            
            // Thêm tất cả fields vào set
            fields.forEach(field => allFieldsSet.add(field));
            Object.keys(extFieldMap).forEach(field => allFieldsSet.add(field));
            
        } catch (error) {
            console.error(`Lỗi khi xử lý file ${sqlFile}:`, error);
        }
    });
    
    // Chuyển set thành array và sắp xếp
    const allFields = Array.from(allFieldsSet).sort();
    console.log(`\nTổng cộng tìm thấy ${allFields.length} fields duy nhất`);
    
    // Tạo CSV
    const csvContent = createCsv(viewsData, allFields);
    
    // Ghi file CSV
    const outputFile = 'views_analysis.csv';
    fs.writeFileSync(outputFile, csvContent, 'utf8');
    
    console.log(`\nĐã tạo file CSV: ${outputFile}`);
    console.log('Hoàn thành!');
}

// Chạy chương trình
if (require.main === module) {
    main();
}

module.exports = {
    readJobConfig,
    getSqlFiles,
    extractFieldsFromSql,
    getExtFieldMapForView,
    createCsv
};
