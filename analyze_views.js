const fs = require('fs');
const path = require('path');

// Đọc file job.json
function readJobConfig() {
    try {
        const jobData = fs.readFileSync('job.json', 'utf8');
        return JSON.parse(jobData);
    } catch (error) {
        console.error('Lỗi khi đọc file job.json:', error);
        return null;
    }
}

// Lấy danh sách tất cả file SQL (trừ table.sql)
function getSqlFiles() {
    const files = fs.readdirSync('.')
        .filter(file => file.endsWith('.sql') && file !== 'table.sql')
        .sort();
    return files;
}

// Trích xuất tên các field từ CREATE VIEW statement và mapping từ SELECT
function extractFieldsFromSql(sqlContent) {
    const fieldMappings = {};

    // Tìm phần CREATE VIEW với danh sách fields trong dấu ngoặc
    const createViewMatch = sqlContent.match(/CREATE\s+OR\s+REPLACE.*?VIEW.*?\((.*?)\)/is);

    if (!createViewMatch) {
        return fieldMappings;
    }

    const fieldsSection = createViewMatch[1];
    const fieldMatches = fieldsSection.match(/"([^"]+)"/g);

    if (!fieldMatches) {
        return fieldMappings;
    }

    const viewFields = fieldMatches.map(match => match.replace(/"/g, ''));

    // Tìm tất cả các phần SELECT trong SQL (có thể có nhiều UNION)
    const allSelectSections = findAllSelectSections(sqlContent);

    if (allSelectSections.length > 0) {
        // Sử dụng SELECT đầu tiên có mapping chi tiết (không phải chỉ liệt kê field names)
        let detailedSelect = null;

        for (const selectSection of allSelectSections) {
            const selectFields = parseSelectFields(selectSection);
            // Kiểm tra xem có phải là SELECT chi tiết không (có table.field hoặc expressions)
            if (selectFields.some(field => field.includes('.') || field.includes('('))) {
                detailedSelect = selectFields;
                break;
            }
        }

        if (detailedSelect) {
            // Map với view fields
            viewFields.forEach((viewField, index) => {
                if (detailedSelect[index]) {
                    fieldMappings[viewField] = detailedSelect[index];
                } else {
                    fieldMappings[viewField] = 'Không xác định';
                }
            });
        } else {
            // Fallback: chỉ ghi tên field
            viewFields.forEach(field => {
                fieldMappings[field] = field;
            });
        }
    }

    return fieldMappings;
}

// Tìm tất cả các phần SELECT trong SQL
function findAllSelectSections(sqlContent) {
    const selectSections = [];

    // Loại bỏ comments
    let cleanSql = sqlContent.replace(/--.*$/gm, '').replace(/\/\*[\s\S]*?\*\//g, '');

    // Tìm tất cả các SELECT
    const selectRegex = /SELECT\s+(.*?)(?=\s+FROM|\s+UNION|\s*$)/gis;
    let match;

    while ((match = selectRegex.exec(cleanSql)) !== null) {
        const selectContent = match[1].trim();
        if (selectContent && !selectContent.match(/^\s*"[^"]+"\s*(?:,\s*"[^"]+"\s*)*$/)) {
            // Không phải chỉ là danh sách field names trong quotes
            selectSections.push(selectContent);
        }
    }

    return selectSections;
}

// Parse SELECT fields để lấy source mapping
function parseSelectFields(selectSection) {
    const fields = [];

    // Loại bỏ comments và normalize whitespace
    let cleanSelect = selectSection.replace(/--.*$/gm, '').replace(/\s+/g, ' ').trim();

    // Tách các field bằng dấu phẩy, nhưng phải cẩn thận với dấu phẩy trong hàm
    const fieldParts = splitSelectFields(cleanSelect);

    fieldParts.forEach(part => {
        const trimmedPart = part.trim();
        if (trimmedPart) {
            // Trích xuất source của field
            const sourceInfo = extractFieldSource(trimmedPart);
            fields.push(sourceInfo);
        }
    });

    return fields;
}

// Tách các field trong SELECT, xử lý dấu phẩy trong hàm
function splitSelectFields(selectText) {
    const fields = [];
    let currentField = '';
    let parenthesesLevel = 0;
    let inQuotes = false;
    let quoteChar = '';

    for (let i = 0; i < selectText.length; i++) {
        const char = selectText[i];

        if (!inQuotes && (char === '"' || char === "'")) {
            inQuotes = true;
            quoteChar = char;
        } else if (inQuotes && char === quoteChar) {
            inQuotes = false;
            quoteChar = '';
        } else if (!inQuotes && char === '(') {
            parenthesesLevel++;
        } else if (!inQuotes && char === ')') {
            parenthesesLevel--;
        } else if (!inQuotes && char === ',' && parenthesesLevel === 0) {
            fields.push(currentField.trim());
            currentField = '';
            continue;
        }

        currentField += char;
    }

    if (currentField.trim()) {
        fields.push(currentField.trim());
    }

    return fields;
}

// Trích xuất source information từ một field expression
function extractFieldSource(fieldExpression) {
    // Loại bỏ quotes xung quanh field names
    let expr = fieldExpression.replace(/"/g, '');

    // Nếu có alias (AS hoặc space), lấy phần trước alias
    const asMatch = expr.match(/^(.*?)\s+(?:AS\s+)?(\w+)$/i);
    if (asMatch) {
        expr = asMatch[1].trim();
    }

    // Các pattern để nhận diện source
    const patterns = [
        // table.field
        /(\w+)\.(\w+)/g,
        // Hàm với table.field bên trong
        /\w+\s*\([^)]*(\w+)\.(\w+)[^)]*\)/g,
        // DECODE, CASE, etc với table.field
        /(?:decode|case|nvl|regexp_replace|to_char)\s*\([^)]*(\w+)\.(\w+)[^)]*\)/gi
    ];

    const sources = new Set();

    patterns.forEach(pattern => {
        let match;
        while ((match = pattern.exec(expr)) !== null) {
            const table = match[1];
            const field = match[2];
            sources.add(`${table}.${field}`);
        }
    });

    if (sources.size > 0) {
        return Array.from(sources).join(', ');
    }

    // Nếu không tìm thấy pattern nào, trả về expression gốc (có thể là literal hoặc complex expression)
    if (expr.includes('(') || expr.includes("'") || /^\d+$/.test(expr.trim())) {
        return 'Literal/Expression';
    }

    return expr.length > 50 ? expr.substring(0, 50) + '...' : expr;
}

// Lấy ext_field_map từ job.json cho một view cụ thể
function getExtFieldMapForView(jobConfig, viewName) {
    if (!jobConfig || !jobConfig.configs || !jobConfig.configs[0] || !jobConfig.configs[0].list) {
        return {};
    }
    
    const viewConfig = jobConfig.configs[0].list.find(item => item.from_table === viewName);
    
    if (viewConfig && viewConfig.ext_field_map) {
        try {
            return JSON.parse(viewConfig.ext_field_map);
        } catch (error) {
            console.error(`Lỗi khi parse ext_field_map cho view ${viewName}:`, error);
            return {};
        }
    }
    
    return {};
}

// Tạo CSV từ dữ liệu
function createCsv(viewsData, allFields) {
    let csv = 'Field';

    // Thêm header cho các view
    Object.keys(viewsData).forEach(viewName => {
        csv += `,${viewName}`;
    });
    csv += '\n';

    // Thêm dữ liệu cho từng field
    allFields.forEach(field => {
        csv += `"${field}"`;

        Object.keys(viewsData).forEach(viewName => {
            const viewData = viewsData[viewName];
            let cellValue = '';

            if (viewData.fieldMappings[field]) {
                cellValue = viewData.fieldMappings[field];
            } else if (viewData.extFieldMap[field]) {
                cellValue = `S_DATA.${field} -> ${viewData.extFieldMap[field]}`;
            } else {
                cellValue = 'Không có';
            }

            csv += `,"${cellValue}"`;
        });

        csv += '\n';
    });

    return csv;
}

// Hàm chính
function main() {
    console.log('Bắt đầu phân tích các file SQL...');
    
    // Đọc cấu hình job
    const jobConfig = readJobConfig();
    if (!jobConfig) {
        console.error('Không thể đọc file job.json');
        return;
    }
    
    // Lấy danh sách file SQL
    const sqlFiles = getSqlFiles();
    console.log(`Tìm thấy ${sqlFiles.length} file SQL:`, sqlFiles);
    
    const viewsData = {};
    const allFieldsSet = new Set();
    
    // Xử lý từng file SQL
    sqlFiles.forEach(sqlFile => {
        console.log(`Đang xử lý file: ${sqlFile}`);
        
        try {
            const sqlContent = fs.readFileSync(sqlFile, 'utf8');
            const viewName = path.basename(sqlFile, '.sql');

            // Trích xuất field mappings từ SQL
            const fieldMappings = extractFieldsFromSql(sqlContent);
            console.log(`  - Tìm thấy ${Object.keys(fieldMappings).length} fields trong ${viewName}`);

            // Lấy ext_field_map từ job.json
            const extFieldMap = getExtFieldMapForView(jobConfig, viewName);
            console.log(`  - Tìm thấy ${Object.keys(extFieldMap).length} ext_field_map entries cho ${viewName}`);

            // Lưu dữ liệu view
            viewsData[viewName] = {
                fieldMappings: fieldMappings,
                extFieldMap: extFieldMap
            };

            // Thêm tất cả fields vào set
            Object.keys(fieldMappings).forEach(field => allFieldsSet.add(field));
            Object.keys(extFieldMap).forEach(field => allFieldsSet.add(field));

        } catch (error) {
            console.error(`Lỗi khi xử lý file ${sqlFile}:`, error);
        }
    });
    
    // Chuyển set thành array và sắp xếp
    const allFields = Array.from(allFieldsSet).sort();
    console.log(`\nTổng cộng tìm thấy ${allFields.length} fields duy nhất`);
    
    // Tạo CSV
    const csvContent = createCsv(viewsData, allFields);
    
    // Ghi file CSV
    const outputFile = 'views_analysis.csv';
    fs.writeFileSync(outputFile, csvContent, 'utf8');
    
    console.log(`\nĐã tạo file CSV: ${outputFile}`);
    console.log('Hoàn thành!');
}

// Chạy chương trình
if (require.main === module) {
    main();
}

module.exports = {
    readJobConfig,
    getSqlFiles,
    extractFieldsFromSql,
    getExtFieldMapForView,
    createCsv
};
