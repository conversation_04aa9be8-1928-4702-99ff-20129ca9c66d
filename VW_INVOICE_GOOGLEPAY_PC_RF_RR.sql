-- ONEDATA.VW_INVOICE_GOOGLEPAY_PC_RF_RR source

CREATE OR REPLACE FORCE EDITIONABLE VIEW "ONEDATA"."VW_INVOICE_GOOGLEPAY_PC_RF_RR" ("S_INVOICE_ID", "<PERSON>_<PERSON>E<PERSON>", "D_UPDATE_INVOICE", "N_ORDER_TXN_ID", "S_TRANS_TYPE", "S_APP_NAME", "S_TRANSACTIONID", "D_DATE", "S_MERCHANTID", "S_INVOICE_MERCHANTID", "S_ORDERREFERENCE", "S_ORDERID", "S_ME<PERSON>HANT<PERSON>ANSACTIONREFEREN", "S_TRANSACTIONTYPE", "S_BATCHNUMBER", "S_CURRENCY", "N_AMOUNT", "S_RRN", "S_RESPONSE_CODE", "S_ACQUIRERRESPONSECODE", "S_O<PERSON>ERATORID", "S_MERCHANT<PERSON>ANSACTIONSOURCE", "S_OR<PERSON><PERSON>AT<PERSON>", "S_<PERSON>RDNO", "S_CARD_TYPE", "S_CARD_EXP", "S_NAME_ON_CARD", "S_FRAUD", "S_FRAUD_DESCRIPTION", "S_FRAUDDATE", "S_AUTHORISED_AMOUNT", "S_CAPTURED_AMOUNT", "S_REFUNDED_AMOUNT", "S_PAYMENT_METHOD", "S_INTEGRATION_TYPE", "S_INTEGRATION_TYPE_VERSION", "S_EMAIL_MERCH", "S_EMAIL_CUSTOMER", "S_EMAIL_FRAUD", "D_SETTLEMENT", "N_VERSION", "S_PAYGATE", "S_STATUS", "S_REASON_NAME", "S_DATA", "S_VOID_DESC", "D_UPDATE", "D_UPDATE7", "S_CUSTOMER_NAME", "S_ORDER_NOTE", "S_CUSTOMER_EMAIL", "S_USER_ID", "S_ORG_MERCH_REF", "S_ACQUIRERID", "S_AUTHORISATION_CODE") DEFAULT COLLATION "USING_NLS_COMP"  AS 
  SELECT
        "S_INVOICE_ID",
        "S_KEY",
        "D_UPDATE_INVOICE",
        "N_ORDER_TXN_ID",
        "S_TRANS_TYPE",
        "S_APP_NAME",
        "S_TRANSACTIONID",
        "D_DATE",
        "S_MERCHANTID",
        "S_INVOICE_MERCHANTID",
        "S_ORDERREFERENCE",
        "S_ORDERID",
        "S_MERCHANTTRANSACTIONREFEREN",
        "S_TRANSACTIONTYPE",
        "S_BATCHNUMBER",
        "S_CURRENCY",
        "N_AMOUNT",
        "S_RRN",
        "S_RESPONSE_CODE",
        "S_ACQUIRERRESPONSECODE",
        "S_OPERATORID",
        "S_MERCHANTTRANSACTIONSOURCE",
        "S_ORDERDATE",
        "S_CARDNO",
        "S_CARD_TYPE",
        "S_CARD_EXP",
        "S_NAME_ON_CARD",
        "S_FRAUD",
        "S_FRAUD_DESCRIPTION",
        "S_FRAUDDATE",
        "S_AUTHORISED_AMOUNT",
        "S_CAPTURED_AMOUNT",
        "S_REFUNDED_AMOUNT",
        "S_PAYMENT_METHOD",
        "S_INTEGRATION_TYPE",
        "S_INTEGRATION_TYPE_VERSION",
        "S_EMAIL_MERCH",
        "S_EMAIL_CUSTOMER",
        "S_EMAIL_FRAUD",
        "D_SETTLEMENT",
        "N_VERSION",
        "S_PAYGATE",
        "S_STATUS",
        "S_REASON_NAME",
        "S_DATA",
        "S_VOID_DESC",
        "D_UPDATE",
        "D_UPDATE7",
        "S_CUSTOMER_NAME",
        "S_ORDER_NOTE",
        "S_CUSTOMER_EMAIL",
        "S_USER_ID",
        "S_ORG_MERCH_REF",
        "S_ACQUIRERID",
        "S_AUTHORISATION_CODE"
    FROM
        (
            SELECT
                i.s_id                                                          s_invoice_id,
                p.s_id                                                          s_key,
                i.d_update                                                      d_update_invoice,
                ot.n_transaction_id                                             n_order_txn_id,
                'GOOGLEPAY'                                                     s_trans_type,
                ''                                                              s_app_name,
                nvl(regexp_substr(p.s_data, '"id"\s*:\s*(\d+)', 1, 1, NULL,
                                  1),
                    p.s_id)                                                     s_transactionid,
                p.d_create                                                      d_date,
                i.s_merchant_id                                                 s_merchantid,
                o.s_merchant_id                                                 s_invoice_merchantid,
                i.s_info                                                        s_orderreference,
                to_char(o.n_ordered_id)                                         s_orderid,
                p.s_client_merch_txn_ref                                        s_merchanttransactionreferen,
                'Purchase'                                                      s_transactiontype,
                ''                                                              s_batchnumber,
                p.s_currency                                                    s_currency,
                ( p.n_amount * 100 )                                            n_amount,
                ''                                                              s_rrn,
                decode(p.s_state,
                       'approved',
                       '0',
                       invoice_get_response_code_2(p.s_reason_name, p.s_state)) s_response_code,
                ''                                                              s_acquirerresponsecode,
                ''                                                              s_operatorid,
                ''                                                              s_merchanttransactionsource,
                ''                                                              s_orderdate,
                p.s_ins_number                                                  s_cardno,
                decode(p.s_ins_brand_id, 'mastercard', 'Mastercard', 'visa', 'Visa',
                       'amex', 'Amex', 'jcb', 'JCB', 'cup',
                       'CUP', 'pp', 'PP', '')                                   s_card_type,
                p.s_ins_month
                || '/'
                || p.s_ins_year                                                 s_card_exp,
                p.s_ins_name                                                    s_name_on_card,
                ''                                                              s_fraud,
                ''                                                              s_fraud_description,
                ''                                                              s_frauddate,
                ''                                                              s_authorised_amount,
                ''                                                              s_captured_amount,
                ''                                                              s_refunded_amount,
                ''                                                              s_payment_method,
                ''                                                              s_integration_type,
                ''                                                              s_integration_type_version,
                ''                                                              s_email_merch,
                ''                                                              s_email_customer,
                ''                                                              s_email_fraud,
                p.d_settlement                                                  d_settlement,
                2                                                               n_version,
                decode(p.s_psp_id, 'ONECREDIT', 'QT')                           s_paygate,
                ''                                                              s_status,
                ''                                                              s_reason_name,
                p.s_data                                                        s_data,
                ''                                                              s_void_desc,
                p.d_update                                                      d_update,
                p.d_update                                                      d_update7,
                o.s_customer_name                                               s_customer_name,
                o.s_order_note                                                  s_order_note,
                o.s_customer_email                                              s_customer_email,
                o.s_user_id                                                     s_user_id,
                to_char(ot.n_transaction_id)                                    s_org_merch_ref,
                nvl(regexp_substr(p.s_data, '"acquirer_id"\s*:\s*"([^"]+)"', 1, 1, NULL,
                                  1),
                    '')                                                         s_acquirerid,
                nvl(regexp_substr(p.s_data, '"authorisation_code"\s*:\s*"([^"]+)"', 1, 1, NULL,
                                  1),
                    '')                                                         s_authorisation_code
            FROM
                     tb_invoice_order o
                INNER JOIN tb_invoice_order_trans ot ON ( o.n_ordered_id = ot.n_order_id )
                INNER JOIN tb_msp_invoice         i ON ( ot.s_transaction_id = i.s_merch_invoice_ref
                                                 AND o.s_order_info = i.s_info )
                INNER JOIN tb_msp_payment         p ON p.s_invoice_id = i.s_id
            WHERE
                ( ot.s_paygate = 'GOOGLEPAY'
                  OR ot.s_paygate = 'MSP' )
                AND p.s_psp_id = 'ONECREDIT'
                AND p.s_ins_type = 'googlepay'
                AND p.d_update > sysdate - INTERVAL '1' DAY
            UNION ALL
            SELECT
                invoice.s_id                   s_invoice_id,
                ref.s_key                      s_key,
                invoice.d_update               d_update_invoice,
                tbot.n_transaction_id          n_order_txn_id,
                'GOOGLEPAY'                    s_trans_type,
                ''                             s_app_name,
                ref.s_transactionid            s_transactionid,
                ref.d_date                     d_date,
                invoice.s_merchant_id          s_merchantid,
                orders.s_merchant_id           s_invoice_merchantid,
                orders.s_order_info            s_orderreference,
                to_char(orders.n_ordered_id)   s_orderid,
                payment.s_client_merch_txn_ref s_merchanttransactionreferen,
                ref.s_transactiontype          s_transactiontype,
                ''                             s_batchnumber,
                payment.s_currency             s_currency,
                ref.n_amount                   n_amount,
                ''                             s_rrn,
                ref.s_response_code            s_response_code,
                ''                             s_acquirerresponsecode,
                ref.s_operatorid               s_operatorid,
                ''                             s_merchanttransactionsource,
                ''                             s_orderdate,
                nvl(payment.s_ins_number, '')  s_cardno,
                decode(payment.s_ins_brand_id, 'mastercard', 'Mastercard', 'visa', 'Visa',
                       'amex', 'Amex', 'jcb', 'JCB', 'cup',
                       'CUP', 'pp', 'PP', '')  s_card_type,
                payment.s_ins_month
                || '/'
                || payment.s_ins_year          s_card_exp,
                ''                             s_name_on_card,
                ''                             s_fraud,
                ''                             s_fraud_description,
                ''                             s_frauddate,
                ''                             s_authorised_amount,
                ''                             s_captured_amount,
                ''                             s_refunded_amount,
                ''                             s_payment_method,
                ''                             s_integration_type,
                ''                             s_integration_type_version,
                ''                             s_email_merch,
                ''                             s_email_customer,
                ''                             s_email_fraud,
                ref.d_settlement               d_settlement,
                2                              n_version,
                'QT'                           s_paygate,
                ''                             s_status,
                ''                             s_reason_name,
                payment.s_data                 s_data,
                ''                             s_void_desc,
                ref.d_update                   d_update,
                ref.d_update                   d_update7,
                orders.s_customer_name         s_customer_name,
                orders.s_order_note            s_order_note,
                orders.s_customer_email        s_customer_email,
                orders.s_user_id               s_user_id,
                to_char(tbot.n_transaction_id) s_org_merch_ref,
                nvl(regexp_substr(payment.s_data, '"acquirer_id"\s*:\s*"([^"]+)"', 1, 1, NULL,
                                  1),
                    '')                        s_acquirerid,
                nvl(regexp_substr(payment.s_data, '"authorisation_code"\s*:\s*"([^"]+)"', 1, 1, NULL,
                                  1),
                    '')                        s_authorisation_code
            FROM
                     tb_invoice_order orders
                INNER JOIN tb_invoice_order_trans tbot ON ( orders.n_ordered_id = tbot.n_order_id
                                                            AND ( tbot.s_paygate = 'GOOGLEPAY'
                                                                  OR tbot.s_paygate = 'MSP' ) )
                INNER JOIN tb_msp_invoice         invoice ON ( tbot.s_transaction_id = invoice.s_merch_invoice_ref
                                                       AND orders.s_order_info = invoice.s_info )
                INNER JOIN tb_msp_payment         payment ON payment.s_invoice_id = invoice.s_id
                INNER JOIN (
                    SELECT
                        to_char(rr.n_id)                                                     s_key,
                        to_char(rr.n_id)                                                     s_transactionid,
                        rr.d_create                                                          d_date,
                        'Request Refund'                                                     s_transactiontype,
                        ( rr.n_amount * 100 )                                                n_amount,
                        invoice_convert_refund_request(rr.s_state, rr.s_process_state, 'QT') s_response_code,
                        regexp_substr(rr.s_refund_request, '"vpc_Operator"\s*:\s*"([^"]+)"', 1, 1, NULL,
                                      1)                                                     s_operatorid,
                        rr.d_create                                                          d_settlement,
                        -- không update lại state của bản ghi request_refund
                        rr.d_update                                                          d_update,
                        rr.s_payment_id                                                      s_payment_id
                    FROM
                        tb_msp_refund_request rr
                    WHERE
                        nvl(regexp_substr(rr.s_data, '"refund_contract_type"\s*:\s*"([^"]+)"', 1, 1, NULL,
                                          1),
                            '') = '2B'
                    UNION ALL
                    SELECT
                        refund.s_id                                  s_key,
                        refund.s_id                                  s_transactionid,
                        refund.d_create                              d_date,
                        'Refund'                                     s_transactiontype,
                        ( refund.n_amount * 100 )                    n_amount,
                        invoice_convert_refund(refund.s_state, 'QT') s_response_code,
                        regexp_substr(refund.s_data, '"operator"\s*:\s*"([^"]+)"', 1, 1, NULL,
                                      1)                             s_operatorid,
                        nvl(refund.d_settlement, '')                 d_settlement,
                        refund.d_update                              d_update,
                        refund.s_payment_id                          s_payment_id
                    FROM
                        tb_msp_refund refund
                )                      ref ON payment.s_id = ref.s_payment_id
            WHERE
                    payment.s_ins_type = 'googlepay'
                AND ref.d_update > sysdate - 1
        );