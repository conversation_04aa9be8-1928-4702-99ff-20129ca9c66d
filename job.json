{"configs": [{"list": [{"type": "sync_realtime", "from_datasource": "ONEDATA_118", "to_datasource": "ONEREPORT_118", "from_table": "VW_INVOICE_QT_ND_PC", "to_table": "TB_RP_ONEINVOICE", "date_field": "D_UPDATE1", "key_fields": ["S_KEY"], "ext_field_map": "{\"S_DATA.acquirer_id\":\"S_ACQUIRERID\",\"S_DATA.authorisation_code\":\"S_AUTHORISATION_CODE\",\"S_DATA.operator\":\"S_OPERATORID\",\"S_DATA.card_type\":\"S_CARD_TYPE\",\"S_DATA.csc_result_code\":\"S_CSCRESULT_CODE\",\"S_DATA.ticket_number\":\"S_TICKET_NUMBER\",\"S_DATA.address\":\"S_ADDRESS\",\"S_DATA.city_town\":\"S_CITY_TOWN\",\"S_DATA.state_province\":\"S_STATE_PROVINCE\",\"S_DATA.zip_postal_code\":\"S_ZIP_POSTAL_CODE\",\"S_DATA.country\":\"S_COUNTRY\",\"S_DATA.avs_result_code\":\"S_AVS_RESULT_CODE\",\"S_DATA.authentication_id\":\"S_PAYMENT_AUTHENTICATION_ID\",\"S_DATA.authentication_type\":\"S_AUTHENTICATION_TYPE\",\"S_DATA.authentication_state\":\"S_AUTHENTICATION_STATE\",\"S_DATA.verification_security_level\":\"S_VERIFICATION_SECURITY_LEVEL\",\"S_DATA.remote_add\":\"S_REMOTE_ADD\",\"S_DATA.locale\":\"S_LOCALE\",\"S_DATA.card_level_indicator\":\"S_CARD_LEVEL_INDICATOR\",\"S_DATA.commercial_card\":\"S_COMMERCIAL_CARD\",\"S_DATA.commercial_card_indicator\":\"S_COMMERCIAL_CARD_INDICATOR\",\"S_DATA.risk_overall_result\":\"S_RISK_OVERALL_RESULT\",\"S_DATA.txn_reversal_result\":\"S_TXN_REVERSAL_RESULT\"}", "interval": "10m", "init_time": "2024-09-25 00:00:00", "actions": ["update", "insert"]}, {"type": "sync_realtime", "from_datasource": "ONEDATA_118", "to_datasource": "ONEREPORT_118", "from_table": "VW_INVOICE_QR_BNPL_PC_V", "to_table": "TB_RP_ONEINVOICE", "date_field": "D_UPDATE2", "key_fields": ["S_KEY"], "interval": "10m", "init_time": "2024-09-25 00:00:00", "actions": ["update", "insert"]}, {"type": "sync_realtime", "from_datasource": "ONEDATA_118", "to_datasource": "ONEREPORT_118", "from_table": "VW_INVOICE_QT_RR_RF3", "to_table": "TB_RP_ONEINVOICE", "key_fields": ["S_KEY"], "date_field": "D_UPDATE3", "interval": "10m", "init_time": "2024-09-25 00:00:00", "ext_field_map": "{\"S_DATA.authorisation_code\":\"S_AUTHORISATION_CODE\",\"S_DATA.card_type\":\"S_CARD_TYPE\",\"S_DATA.csc_result_code\":\"S_CSCRESULT_CODE\",\"S_DATA.ticket_number\":\"S_TICKET_NUMBER\",\"S_DATA.address\":\"S_ADDRESS\",\"S_DATA.city_town\":\"S_CITY_TOWN\",\"S_DATA.state_province\":\"S_STATE_PROVINCE\",\"S_DATA.zip_postal_code\":\"S_ZIP_POSTAL_CODE\",\"S_DATA.country\":\"S_COUNTRY\",\"S_DATA.avs_result_code\":\"S_AVS_RESULT_CODE\",\"S_DATA.authentication_id\":\"S_PAYMENT_AUTHENTICATION_ID\",\"S_DATA.authentication_type\":\"S_AUTHENTICATION_TYPE\",\"S_DATA.authentication_state\":\"S_AUTHENTICATION_STATE\",\"S_DATA.verification_security_level\":\"S_VERIFICATION_SECURITY_LEVEL\",\"S_DATA.remote_add\":\"S_REMOTE_ADD\",\"S_DATA.locale\":\"S_LOCALE\",\"S_DATA.card_level_indicator\":\"S_CARD_LEVEL_INDICATOR\",\"S_DATA.commercial_card\":\"S_COMMERCIAL_CARD\",\"S_DATA.commercial_card_indicator\":\"S_COMMERCIAL_CARD_INDICATOR\",\"S_DATA.risk_overall_result\":\"S_RISK_OVERALL_RESULT\",\"S_DATA.txn_reversal_result\":\"S_TXN_REVERSAL_RESULT\"}", "actions": ["insert", "update"]}, {"type": "sync_realtime", "from_datasource": "ONEDATA_118", "to_datasource": "ONEREPORT_118", "from_table": "VW_INVOICE_BNPL_RR_RF", "to_table": "TB_RP_ONEINVOICE", "key_fields": ["S_KEY"], "date_field": "D_UPDATE3", "interval": "10m", "init_time": "2024-09-25 00:00:00", "ext_field_map": "{\"S_DATA.authorisation_code\":\"S_AUTHORISATION_CODE\",\"S_DATA.card_type\":\"S_CARD_TYPE\",\"S_DATA.csc_result_code\":\"S_CSCRESULT_CODE\",\"S_DATA.ticket_number\":\"S_TICKET_NUMBER\",\"S_DATA.address\":\"S_ADDRESS\",\"S_DATA.city_town\":\"S_CITY_TOWN\",\"S_DATA.state_province\":\"S_STATE_PROVINCE\",\"S_DATA.zip_postal_code\":\"S_ZIP_POSTAL_CODE\",\"S_DATA.country\":\"S_COUNTRY\",\"S_DATA.avs_result_code\":\"S_AVS_RESULT_CODE\",\"S_DATA.authentication_id\":\"S_PAYMENT_AUTHENTICATION_ID\",\"S_DATA.authentication_type\":\"S_AUTHENTICATION_TYPE\",\"S_DATA.authentication_state\":\"S_AUTHENTICATION_STATE\",\"S_DATA.verification_security_level\":\"S_VERIFICATION_SECURITY_LEVEL\",\"S_DATA.remote_add\":\"S_REMOTE_ADD\",\"S_DATA.locale\":\"S_LOCALE\",\"S_DATA.card_level_indicator\":\"S_CARD_LEVEL_INDICATOR\",\"S_DATA.commercial_card\":\"S_COMMERCIAL_CARD\",\"S_DATA.commercial_card_indicator\":\"S_COMMERCIAL_CARD_INDICATOR\",\"S_DATA.risk_overall_result\":\"S_RISK_OVERALL_RESULT\",\"S_DATA.txn_reversal_result\":\"S_TXN_REVERSAL_RESULT\"}", "actions": ["insert", "update"]}, {"type": "sync_realtime", "from_datasource": "ONEDATA_118", "to_datasource": "ONEREPORT_118", "from_table": "VW_INVOICE_QR_RR_RF", "to_table": "TB_RP_ONEINVOICE", "key_fields": ["S_KEY"], "date_field": "D_UPDATE3", "interval": "10m", "init_time": "2024-09-25 00:00:00", "ext_field_map": "{\"S_DATA.authorisation_code\":\"S_AUTHORISATION_CODE\",\"S_DATA.csc_result_code\":\"S_CSCRESULT_CODE\",\"S_DATA.ticket_number\":\"S_TICKET_NUMBER\",\"S_DATA.address\":\"S_ADDRESS\",\"S_DATA.city_town\":\"S_CITY_TOWN\",\"S_DATA.state_province\":\"S_STATE_PROVINCE\",\"S_DATA.zip_postal_code\":\"S_ZIP_POSTAL_CODE\",\"S_DATA.country\":\"S_COUNTRY\",\"S_DATA.avs_result_code\":\"S_AVS_RESULT_CODE\",\"S_DATA.authentication_id\":\"S_PAYMENT_AUTHENTICATION_ID\",\"S_DATA.authentication_type\":\"S_AUTHENTICATION_TYPE\",\"S_DATA.authentication_state\":\"S_AUTHENTICATION_STATE\",\"S_DATA.verification_security_level\":\"S_VERIFICATION_SECURITY_LEVEL\",\"S_DATA.remote_add\":\"S_REMOTE_ADD\",\"S_DATA.locale\":\"S_LOCALE\",\"S_DATA.card_level_indicator\":\"S_CARD_LEVEL_INDICATOR\",\"S_DATA.commercial_card\":\"S_COMMERCIAL_CARD\",\"S_DATA.commercial_card_indicator\":\"S_COMMERCIAL_CARD_INDICATOR\",\"S_DATA.risk_overall_result\":\"S_RISK_OVERALL_RESULT\",\"S_DATA.txn_reversal_result\":\"S_TXN_REVERSAL_RESULT\"}", "actions": ["insert", "update"]}, {"type": "sync_realtime", "from_datasource": "ONEDATA_118", "to_datasource": "ONEREPORT_118", "from_table": "VW_INVOICE_ND_RR_RF", "to_table": "TB_RP_ONEINVOICE", "key_fields": ["S_KEY"], "date_field": "D_UPDATE4", "interval": "10m", "init_time": "2024-09-25 00:00:00", "actions": ["insert", "update"]}, {"type": "sync_interval", "from_datasource": "ONEDATA_118", "to_datasource": "ONEREPORT_118", "from_table": "VW_INVOICE_BNPL_PC", "to_table": "TB_RP_ONEINVOICE", "key_fields": ["S_KEY"], "date_field": "D_UPDATE_INVOICE", "interval": "10m", "actions": ["update"]}, {"type": "sync_realtime", "from_datasource": "ONEDATA_118", "to_datasource": "ONEREPORT_118", "from_table": "VW_INVOICE_APPLEPAY_PC_RF_RR", "to_table": "TB_RP_ONEINVOICE", "key_fields": ["S_KEY"], "date_field": "D_UPDATE6", "interval": "10m", "init_time": "2024-09-25 00:00:00", "actions": ["insert", "update"]}, {"type": "sync_realtime", "from_datasource": "ONEDATA_118", "to_datasource": "ONEREPORT_118", "from_table": "VW_INVOICE_GOOGLEPAY_PC_RF_RR", "to_table": "TB_RP_ONEINVOICE", "key_fields": ["S_KEY"], "date_field": "D_UPDATE7", "interval": "10m", "init_time": "2025-01-06 00:00:00", "actions": ["insert", "update"]}, {"type": "sync_realtime", "from_datasource": "ONEDATA_118", "to_datasource": "ONEREPORT_118", "from_table": "VW_INVOICE_SAMSUNGPAY_PC_RF_RR", "to_table": "TB_RP_ONEINVOICE", "key_fields": ["S_KEY"], "date_field": "D_UPDATE8", "interval": "10m", "init_time": "2025-01-06 00:00:00", "actions": ["insert", "update"]}, {"type": "sync_realtime", "from_datasource": "ONEDATA_118", "to_datasource": "ONEREPORT_118", "from_table": "VW_INVOICE_VIETQR_PC_RF_RR", "to_table": "TB_RP_ONEINVOICE", "key_fields": ["S_KEY"], "date_field": "D_UPDATE9", "interval": "10m", "init_time": "2025-01-20 00:00:00", "actions": ["insert", "update"]}], "run_parallel": "true"}], "sync_info_key": "15010"}