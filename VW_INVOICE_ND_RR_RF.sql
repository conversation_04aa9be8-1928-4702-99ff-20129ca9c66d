-- ONEDATA.VW_INVOICE_ND_RR_RF source

CREATE OR REPLACE FORCE EDITIONABLE VIEW "ONEDATA"."VW_INVOICE_ND_RR_RF" ("S_INVOICE_ID", "S_KEY", "D_UPDATE_INVOICE", "N_ORDER_TXN_ID", "S_TRANS_TYPE", "S_APP_NAME", "S_TRANSACTIONID", "D_DATE", "S_MERCHANTID", "S_INVOICE_MERCHANTID", "S_ORDERREFERENCE", "S_ORDERID", "S_ME<PERSON><PERSON>NT<PERSON>ANSACTIONREFEREN", "S_TRANSACTIONTYPE", "S_ACQUIRERID", "S_BATCHNUMBER", "S_CURRENCY", "N_AMOUNT", "S_RRN", "S_RESPONSE_CODE", "S_ACQUIRERRESPONSECODE", "S_AUTHORISATION_CODE", "S_O<PERSON>ERATORID", "S_ME<PERSON>HANT<PERSON>ANSACTIONSOURCE", "S_ORDERDAT<PERSON>", "S_CARD_TYPE", "S_CARDNO", "S_CARD_EXP", "S_CSCRESULT_CODE", "S_FRAUD", "S_FRAUD_DESCRIPTION", "S_FRAUDDATE", "S_TICKET_NUMBER", "S_AUTHORISED_AMOUNT", "S_CAPTURED_AMOUNT", "S_REFUNDED_AMOUNT", "S_ADDRESS", "S_CITY_TOWN", "S_STATE_PROVINCE", "S_ZIP_POSTAL_CODE", "S_COUNTRY", "S_AVS_RESULT_CODE", "S_PAYMENT_METHOD", "S_INTEGRATION_TYPE", "S_INTEGRATION_TYPE_VERSION", "S_PAYMENT_AUTHENTICATION_ID", "S_AUTHENTICATION_TYPE", "S_AUTHENTICATION_STATE", "S_VERIFICATION_SECURITY_LEVEL", "S_EMAIL_MERCH", "S_EMAIL_CUSTOMER", "S_EMAIL_FRAUD", "S_REMOTE_ADD", "S_LOCALE", "D_SETTLEMENT", "S_CARD_LEVEL_INDICATOR", "S_COMMERCIAL_CARD", "S_COMMERCIAL_CARD_INDICATOR", "S_RISK_OVERALL_RESULT", "S_TXN_REVERSAL_RESULT", "N_VERSION", "S_PAYGATE", "S_STATUS", "S_REASON_NAME", "S_DATA", "S_VOID_DESC", "D_UPDATE", "D_UPDATE4", "S_CUSTOMER_NAME", "S_ORDER_NOTE", "S_CUSTOMER_EMAIL", "S_USER_ID") DEFAULT COLLATION "USING_NLS_COMP"  AS 
  SELECT
        "S_INVOICE_ID",
        "S_KEY",
        "D_UPDATE_INVOICE",
        "N_ORDER_TXN_ID",
        "S_TRANS_TYPE",
        "S_APP_NAME",
        "S_TRANSACTIONID",
        "D_DATE",
        "S_MERCHANTID",
        "S_INVOICE_MERCHANTID",
        "S_ORDERREFERENCE",
        "S_ORDERID",
        "S_MERCHANTTRANSACTIONREFEREN",
        "S_TRANSACTIONTYPE",
        "S_ACQUIRERID",
        "S_BATCHNUMBER",
        "S_CURRENCY",
        "N_AMOUNT",
        "S_RRN",
        "S_RESPONSE_CODE",
        "S_ACQUIRERRESPONSECODE",
        "S_AUTHORISATION_CODE",
        "S_OPERATORID",
        "S_MERCHANTTRANSACTIONSOURCE",
        "S_ORDERDATE",
        "S_CARD_TYPE",
        "S_CARDNO",
        "S_CARD_EXP",
        "S_CSCRESULT_CODE",
        "S_FRAUD",
        "S_FRAUD_DESCRIPTION",
        "S_FRAUDDATE",
        "S_TICKET_NUMBER",
        "S_AUTHORISED_AMOUNT",
        "S_CAPTURED_AMOUNT",
        "S_REFUNDED_AMOUNT",
        "S_ADDRESS",
        "S_CITY_TOWN",
        "S_STATE_PROVINCE",
        "S_ZIP_POSTAL_CODE",
        "S_COUNTRY",
        "S_AVS_RESULT_CODE",
        "S_PAYMENT_METHOD",
        "S_INTEGRATION_TYPE",
        "S_INTEGRATION_TYPE_VERSION",
        "S_PAYMENT_AUTHENTICATION_ID",
        "S_AUTHENTICATION_TYPE",
        "S_AUTHENTICATION_STATE",
        "S_VERIFICATION_SECURITY_LEVEL",
        "S_EMAIL_MERCH",
        "S_EMAIL_CUSTOMER",
        "S_EMAIL_FRAUD",
        "S_REMOTE_ADD",
        "S_LOCALE",
        "D_SETTLEMENT",
        "S_CARD_LEVEL_INDICATOR",
        "S_COMMERCIAL_CARD",
        "S_COMMERCIAL_CARD_INDICATOR",
        "S_RISK_OVERALL_RESULT",
        "S_TXN_REVERSAL_RESULT",
        "N_VERSION",
        "S_PAYGATE",
        "S_STATUS",
        "S_REASON_NAME",
        "S_DATA",
        "S_VOID_DESC",
        "D_UPDATE",
        "D_UPDATE4",
        "S_CUSTOMER_NAME",
        "S_ORDER_NOTE",
        "S_CUSTOMER_EMAIL",
        "S_USER_ID"
    FROM
        (
            SELECT
                invoice.s_id                                                              s_invoice_id,
                to_char(ex.n_log_id)                                                      s_key,
                invoice.d_update                                                          d_update_invoice,
                tbot.n_transaction_id                                                     n_order_txn_id,
                'ND'                                                                      s_trans_type,
                ''                                                                        s_app_name,
                to_char(ex.n_log_id)                                                      s_transactionid,
                ex.d_log_date                                                             d_date,
                tbl.s_merchant_id                                                         s_merchantid,
                orders.s_merchant_id                                                      AS s_invoice_merchantid,
                tbl.s_order_info                                                          s_orderreference,
                to_char(orders.n_ordered_id)                                              s_orderid,
                ex.s_merchant_transaction_ref                                             s_merchanttransactionreferen,
                'Refund'                                                                  s_transactiontype,
                ac.s_acquirer_name                                                        s_acquirerid,
                ''                                                                        s_batchnumber,
                tbl.s_currency_code                                                       s_currency,
                ( ex.n_amount * 100 )                                                     n_amount,
                ''                                                                        s_rrn,
                to_char(decode(ex.n_transaction_status, 400, 0, ex.n_transaction_status)) s_response_code,
                ''                                                                        s_acquirerresponsecode,
                ''                                                                        s_authorisation_code,
                ''                                                                        s_operatorid,
                ''                                                                        s_merchanttransactionsource,
                ''                                                                        s_orderdate,
                ac.s_acquirer_name                                                        s_card_type,
                tbl.s_card_number                                                         s_cardno,
                tbl.s_card_date                                                           s_card_exp,
                ''                                                                        s_cscresult_code,
                ''                                                                        s_fraud,
                ''                                                                        s_fraud_description,
                ''                                                                        s_frauddate,
                tbl.s_ip                                                                  s_ticket_number,
                ''                                                                        s_authorised_amount,
                ''                                                                        s_captured_amount,
                ''                                                                        s_refunded_amount,
                ''                                                                        s_address,
                ''                                                                        s_city_town,
                ''                                                                        s_state_province,
                ''                                                                        s_zip_postal_code,
                ''                                                                        s_country,
                ''                                                                        s_avs_result_code,
                ''                                                                        s_payment_method,
                ''                                                                        s_integration_type,
                ''                                                                        s_integration_type_version,
                ''                                                                        s_payment_authentication_id,
                ''                                                                        s_authentication_type,
                ''                                                                        s_authentication_state,
                ''                                                                        s_verification_security_level,
                ''                                                                        s_email_merch,
                ''                                                                        s_email_customer,
                ''                                                                        s_email_fraud,
                tbl.s_ip                                                                  s_remote_add,
                tbl.s_transation_language                                                 s_locale,
                ex.d_log_date                                                             d_settlement,
                ''                                                                        s_card_level_indicator,
                ''                                                                        s_commercial_card,
                ''                                                                        s_commercial_card_indicator,
                ''                                                                        s_risk_overall_result,
                ''                                                                        s_txn_reversal_result,
                2                                                                         n_version,
                'ND'                                                                      s_paygate,
                ''                                                                        s_status,
                ''                                                                        s_reason_name,
                ''                                                                        s_data,
                ''                                                                        s_void_desc,
                ex.d_update,
                ex.d_update                                                               d_update4,
                orders.s_customer_name,
                orders.s_order_note,
                orders.s_customer_email,
                orders.s_user_id
            FROM
                     tb_invoice_order orders
                INNER JOIN tb_invoice_order_trans tbot ON orders.n_ordered_id = tbot.n_order_id
                                                          AND ( tbot.s_paygate = 'MSP'
                                                                OR tbot.s_paygate = 'ND' )
                INNER JOIN tb_msp_invoice         invoice ON ( to_char(tbot.n_transaction_id) = invoice.s_merch_invoice_ref
                                                       AND orders.s_order_info = invoice.s_info )
                INNER JOIN tb_msp_payment         payment ON payment.s_invoice_id = invoice.s_id
                INNER JOIN tb_p2_transaction      tbl ON ( payment.s_client_merch_txn_ref = tbl.s_merchant_transaction_ref
                                                      AND payment.s_client_merchant_id = tbl.s_merchant_id )
                INNER JOIN tb_p2_transaction_log  ex ON ex.n_transaction_id = tbl.n_transaction_id
                INNER JOIN tb_p2_acquirer         ac ON tbl.n_bank_id = ac.n_acquirer_id
                INNER JOIN tb_msp_refund          refund ON payment.s_id = refund.s_payment_id
            WHERE
                    ex.d_update >= sysdate - INTERVAL '1' DAY
                AND ex.s_transaction_type = 'Refund'
                AND TRIM(regexp_replace(regexp_substr(payment.s_data, '"contract_type":"((\\"|[^"])*)'),
                                        '"contract_type":"')) = '3B'
                --refund 2B
            UNION ALL
            SELECT
                invoice.s_id                                                 s_invoice_id,
                to_char(rr.n_id)                                             s_key,
                invoice.d_update                                             d_update_invoice,
                tbot.n_transaction_id                                        n_order_txn_id,
                'ND'                                                         s_trans_type,
                ''                                                           s_app_name,
                to_char(rr.n_id)                                             s_transactionid,
                rr.d_create                                                  d_date,
                tbl.s_merchant_id                                            s_merchantid,
                orders.s_merchant_id                                         s_invoice_merchantid,
                tbl.s_order_info                                             s_orderreference,
                to_char(orders.n_ordered_id)                                 s_orderid,
                tbl.s_merchant_transaction_ref                               s_merchanttransactionreferen,
                'Request Refund'                                             s_transactiontype,
                ac.s_acquirer_name                                           s_acquirerid,
                ''                                                           s_batchnumber,
                tbl.s_currency_code                                          s_currency,
                ( rr.n_amount * 100 )                                        n_amount,
                ''                                                           s_rrn,
                invoice_convert_refund_request(rr.s_state, rr.s_process_state, 'ND') s_response_code,
                ''                                                           s_acquirerresponsecode,
                ''                                                           s_authorisation_code,
                replace(regexp_replace(regexp_substr(rr.s_data, '"operator":"((\\"|[^"])*)"'),
                                       '"operator":'),
                        '"')                                                 s_operatorid,
                ''                                                           s_merchanttransactionsource,
                ''                                                           s_orderdate,
                ac.s_acquirer_name                                           s_card_type,
                tbl.s_card_number                                            s_cardno,
                tbl.s_card_date                                              s_card_exp,
                ''                                                           s_cscresult_code,
                ''                                                           s_fraud,
                ''                                                           s_fraud_description,
                ''                                                           s_frauddate,
                tbl.s_ip                                                     s_ticket_number,
                ''                                                           s_authorised_amount,
                ''                                                           s_captured_amount,
                ''                                                           s_refunded_amount,
                ''                                                           s_address,
                ''                                                           s_city_town,
                ''                                                           s_state_province,
                ''                                                           s_zip_postal_code,
                ''                                                           s_country,
                ''                                                           s_avs_result_code,
                ''                                                           s_payment_method,
                ''                                                           s_integration_type,
                ''                                                           s_integration_type_version,
                ''                                                           s_payment_authentication_id,
                ''                                                           s_authentication_type,
                ''                                                           s_authentication_state,
                ''                                                           s_verification_security_level,
                ''                                                           s_email_merch,
                ''                                                           s_email_customer,
                ''                                                           s_email_fraud,
                tbl.s_ip                                                     s_remote_add,
                tbl.s_transation_language                                    s_locale,
                rr.d_create                                                  d_settlement,
                ''                                                           s_card_level_indicator,
                ''                                                           s_commercial_card,
                ''                                                           s_commercial_card_indicator,
                ''                                                           s_risk_overall_result,
                ''                                                           s_txn_reversal_result,
                2                                                            n_version,
                'ND'                                                         s_paygate,
                ''                                                           s_status,
                ''                                                           s_reason_name,
                ''                                                           s_data,
                ''                                                           s_void_desc,
                rr.d_update                                                  d_update,
                rr.d_update                                                  d_update4,
                orders.s_customer_name                                       s_customer_name,
                orders.s_order_note                                          s_order_note,
                orders.s_customer_email                                      s_customer_email,
                orders.s_user_id                                             s_user_id
            FROM
                     tb_invoice_order orders
                INNER JOIN tb_invoice_order_trans tbot ON ( orders.n_ordered_id = tbot.n_order_id
                                                            AND ( tbot.s_paygate = 'MSP'
                                                                  OR tbot.s_paygate = 'ND' ) )
                INNER JOIN tb_msp_invoice         invoice ON ( to_char(tbot.n_transaction_id) = invoice.s_merch_invoice_ref
                                                       AND orders.s_order_info = invoice.s_info )
                INNER JOIN tb_msp_payment         payment ON payment.s_invoice_id = invoice.s_id
                INNER JOIN tb_p2_transaction      tbl ON ( payment.s_client_merch_txn_ref = tbl.s_merchant_transaction_ref
                                                      AND payment.s_client_merchant_id = tbl.s_merchant_id )
                INNER JOIN tb_p2_acquirer         ac ON tbl.n_bank_id = ac.n_acquirer_id
                INNER JOIN tb_msp_refund_request  rr ON payment.s_id = rr.s_payment_id
            WHERE
                    rr.d_update >= sysdate - INTERVAL '1' DAY
                AND TRIM(regexp_replace(regexp_substr(rr.s_data, '"refund_contract_type":"((\\"|[^"])*)'),
                                        '"refund_contract_type":"')) = '2B'
            UNION ALL
            SELECT
                invoice.s_id                         s_invoice_id,
                refund.s_id                          s_key,
                invoice.d_update                     d_update_invoice,
                tbot.n_transaction_id                n_order_txn_id,
                'ND'                                 s_trans_type,
                ''                                   s_app_name,
                refund.s_id                          s_transactionid,
                refund.d_create                      d_date,
                tbl.s_merchant_id                    s_merchantid,
                orders.s_merchant_id                 s_invoice_merchantid,
                tbl.s_order_info                     s_orderreference,
                to_char(orders.n_ordered_id)         s_orderid,
                tbl.s_merchant_transaction_ref       s_merchanttransactionreferen,
                'Refund'                             s_transactiontype,
                ac.s_acquirer_name                   s_acquirerid,
                ''                                   s_batchnumber,
                tbl.s_currency_code                  s_currency,
                ( refund.n_amount * 100 )            n_amount,
                ''                                   s_rrn,
                invoice_convert_refund(refund.s_state, 'ND') s_response_code,
                ''                                   s_acquirerresponsecode,
                ''                                   s_authorisation_code,
                replace(regexp_replace(regexp_substr(refund.s_description, '"operator":"((\\"|[^"])*)"'),
                                       '"operator":'),
                        '"')                         s_operatorid,
                ''                                   s_merchanttransactionsource,
                ''                                   s_orderdate,
                ac.s_acquirer_name                   s_card_type,
                tbl.s_card_number                    s_cardno,
                tbl.s_card_date                      s_card_exp,
                ''                                   s_cscresult_code,
                ''                                   s_fraud,
                ''                                   s_fraud_description,
                ''                                   s_frauddate,
                tbl.s_ip                             s_ticket_number,
                ''                                   s_authorised_amount,
                ''                                   s_captured_amount,
                ''                                   s_refunded_amount,
                ''                                   s_address,
                ''                                   s_city_town,
                ''                                   s_state_province,
                ''                                   s_zip_postal_code,
                ''                                   s_country,
                ''                                   s_avs_result_code,
                ''                                   s_payment_method,
                ''                                   s_integration_type,
                ''                                   s_integration_type_version,
                ''                                   s_payment_authentication_id,
                ''                                   s_authentication_type,
                ''                                   s_authentication_state,
                ''                                   s_verification_security_level,
                ''                                   s_email_merch,
                ''                                   s_email_customer,
                ''                                   s_email_fraud,
                tbl.s_ip                             s_remote_add,
                tbl.s_transation_language            s_locale,
                nvl(refund.d_settlement, '')         d_settlement,
                ''                                   s_card_level_indicator,
                ''                                   s_commercial_card,
                ''                                   s_commercial_card_indicator,
                ''                                   s_risk_overall_result,
                ''                                   s_txn_reversal_result,
                2                                    n_version,
                'ND'                                 s_paygate,
                ''                                   s_status,
                ''                                   s_reason_name,
                ''                                   s_data,
                ''                                   s_void_desc,
                refund.d_update                      d_update,
                refund.d_update                      d_update4,
                orders.s_customer_name               s_customer_name,
                orders.s_order_note                  s_order_note,
                orders.s_customer_email              s_customer_email,
                orders.s_user_id                     s_user_id
            FROM
                     tb_invoice_order orders
                INNER JOIN tb_invoice_order_trans tbot ON ( orders.n_ordered_id = tbot.n_order_id
                                                            AND ( tbot.s_paygate = 'MSP'
                                                                  OR tbot.s_paygate = 'ND' ) )
                INNER JOIN tb_msp_invoice         invoice ON ( to_char(tbot.n_transaction_id) = invoice.s_merch_invoice_ref
                                                       AND orders.s_order_info = invoice.s_info )
                INNER JOIN tb_msp_payment         payment ON payment.s_invoice_id = invoice.s_id
                INNER JOIN tb_p2_transaction      tbl ON ( payment.s_client_merch_txn_ref = tbl.s_merchant_transaction_ref
                                                      AND payment.s_client_merchant_id = tbl.s_merchant_id )
                INNER JOIN tb_p2_acquirer         ac ON tbl.n_bank_id = ac.n_acquirer_id
                INNER JOIN tb_msp_refund          refund ON payment.s_id = refund.s_payment_id
            WHERE
                    refund.d_update >= sysdate - INTERVAL '1' DAY
                AND TRIM(regexp_replace(regexp_substr(payment.s_data, '"contract_type":"((\\"|[^"])*)'),
                                        '"contract_type":"')) = '2B'
        );