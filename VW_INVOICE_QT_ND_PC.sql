-- ONEDATA.VW_INVOICE_QT_ND_PC source

CREATE OR REPLACE FORCE EDITIONABLE VIEW "ONEDATA"."VW_INVOICE_QT_ND_PC" ("S_INVOICE_ID", "<PERSON>_KEY", "D_UPDATE_INVOICE", "N_ORDER_TXN_ID", "S_TRANS_TYPE", "S_APP_NAME", "S_TRANSACTIONID", "D_DATE", "S_MERCHANTID", "S_INVOICE_MERCHANTID", "S_ORDERREFERENCE", "S_ORDERID", "S_ME<PERSON>HANT<PERSON>ANSACTIONREFEREN", "S_TRANSACTIONTYPE", "S_<PERSON>TCHNUMBER", "S_CURRENCY", "N_AMOUNT", "S_RRN", "S_RESPONSE_CODE", "S_ACQUIRERRESPONSECODE", "S_OPERATOR<PERSON>", "S_MERCHANT<PERSON>ANSACTIONSOURCE", "S_ORDERDAT<PERSON>", "S_<PERSON>RDNO", "S_CARD_EXP", "S_NAME_ON_CARD", "S_FRAUD", "S_FRAUD_DESCRIPTION", "S_FRAUDDATE", "S_AUTHORISED_AMOUNT", "S_CAPTURED_AMOUNT", "S_REFUNDED_AMOUNT", "S_PAYMENT_METHOD", "S_INTEGRATION_TYPE", "S_INTEGRATION_TYPE_VERSION", "S_EMAIL_MERCH", "S_EMAIL_CUSTOMER", "S_EMAIL_FRAUD", "D_SETTLEMENT", "N_VERSION", "S_PAYGATE", "S_STATUS", "S_REASON_NAME", "S_DATA", "S_VOID_DESC", "D_UPDATE", "D_UPDATE1", "S_CUSTOMER_NAME", "S_ORDER_NOTE", "S_CUSTOMER_EMAIL", "S_USER_ID", "S_ORG_MERCH_REF") DEFAULT COLLATION "USING_NLS_COMP"  AS 
  SELECT
        "S_INVOICE_ID",
        "S_KEY",
        "D_UPDATE_INVOICE",
        "N_ORDER_TXN_ID",
        "S_TRANS_TYPE",
        "S_APP_NAME",
        "S_TRANSACTIONID",
        "D_DATE",
        "S_MERCHANTID",
        "S_INVOICE_MERCHANTID",
        "S_ORDERREFERENCE",
        "S_ORDERID",
        "S_MERCHANTTRANSACTIONREFEREN",
        "S_TRANSACTIONTYPE",
        "S_BATCHNUMBER",
        "S_CURRENCY",
        "N_AMOUNT",
        "S_RRN",
        "S_RESPONSE_CODE",
        "S_ACQUIRERRESPONSECODE",
        "S_OPERATORID",
        "S_MERCHANTTRANSACTIONSOURCE",
        "S_ORDERDATE",
        "S_CARDNO",
        "S_CARD_EXP",
        "S_NAME_ON_CARD",
        "S_FRAUD",
        "S_FRAUD_DESCRIPTION",
        "S_FRAUDDATE",
        "S_AUTHORISED_AMOUNT",
        "S_CAPTURED_AMOUNT",
        "S_REFUNDED_AMOUNT",
        "S_PAYMENT_METHOD",
        "S_INTEGRATION_TYPE",
        "S_INTEGRATION_TYPE_VERSION",
        "S_EMAIL_MERCH",
        "S_EMAIL_CUSTOMER",
        "S_EMAIL_FRAUD",
        "D_SETTLEMENT",
        "N_VERSION",
        "S_PAYGATE",
        "S_STATUS",
        "S_REASON_NAME",
        "S_DATA",
        "S_VOID_DESC",
        "D_UPDATE",
        "D_UPDATE1",
        "S_CUSTOMER_NAME",
        "S_ORDER_NOTE",
        "S_CUSTOMER_EMAIL",
        "S_USER_ID",
        "S_ORG_MERCH_REF"
    FROM
        (
            SELECT
                i.s_id                                                          s_invoice_id,
                p.s_id                                                          s_key,
                i.d_update                                                      d_update_invoice,
                ot.n_transaction_id                                             n_order_txn_id,
                decode(p.s_psp_id, 'ONECREDIT', 'QT')                           s_trans_type,
                ''                                                              s_app_name,
                decode(instr(p.s_data, '"id"'),
                       0,
                       p.s_settlement_ref,
                       nvl(regexp_replace(p.s_data, '.*"id":(\d*),.*', '\1'),
                           p.s_settlement_ref))                                 s_transactionid,
                p.d_create                                                      d_date,
                i.s_merchant_id                                                 s_merchantid,
                o.s_merchant_id                                                 AS s_invoice_merchantid,
                i.s_info                                                        s_orderreference,
                to_char(o.n_ordered_id)                                         s_orderid,
                p.s_client_merch_txn_ref                                        s_merchanttransactionreferen,
                'Purchase'                                                      s_transactiontype,
                ''                                                              s_batchnumber,
                p.s_currency,
                ( p.n_amount * 100 )                                            AS n_amount,
                ''                                                              s_rrn,
                decode(p.s_state,
                       'approved',
                       '0',
                       invoice_get_response_code_2(p.s_reason_name, p.s_state)) s_response_code,
                ''                                                              s_acquirerresponsecode,
                ''                                                              s_operatorid,
                ''                                                              s_merchanttransactionsource,
                ''                                                              s_orderdate,
                p.s_ins_number                                                  s_cardno,
                decode(p.s_ins_brand_id, 'mastercard', 'Mastercard', 'visa', 'Visa',
                       'amex', 'Amex', 'jcb', 'JCB', 'cup',
                       'CUP', 'pp', 'PP', '')                                   s_card_type,
                p.s_ins_month
                || '/'
                || p.s_ins_year                                                 s_card_exp,
                p.s_ins_name                                                    s_name_on_card,
                ''                                                              s_fraud,
                ''                                                              s_fraud_description,
                ''                                                              s_frauddate,
                ''                                                              s_authorised_amount,
                ''                                                              s_captured_amount,
                ''                                                              s_refunded_amount,
                ''                                                              s_payment_method,
                ''                                                              s_integration_type,
                ''                                                              s_integration_type_version,
                ''                                                              s_email_merch,
                ''                                                              s_email_customer,
                ''                                                              s_email_fraud,
                p.d_settlement,
                2                                                               n_version,
                decode(p.s_psp_id, 'ONECREDIT', 'QT')                           s_paygate,
                ''                                                              s_status,
                ''                                                              s_reason_name,
                p.s_data                                                        s_data,
                ''                                                              s_void_desc,
                p.d_update                                                      d_update,
                p.d_update                                                      d_update1,
                o.s_customer_name,
                o.s_order_note,
                o.s_customer_email,
                o.s_user_id,
                to_char(ot.n_transaction_id)                                    s_org_merch_ref
            FROM
                     tb_invoice_order o
                INNER JOIN tb_invoice_order_trans ot ON ( o.n_ordered_id = ot.n_order_id )
                INNER JOIN tb_msp_invoice         i ON ( to_char(ot.n_transaction_id) = i.s_merch_invoice_ref
                                                 AND o.s_order_info = i.s_info )
                INNER JOIN tb_msp_payment         p ON p.s_invoice_id = i.s_id
            WHERE
                p.s_ins_type NOT IN ( 'applepay', 'googlepay', 'samsungpay' )
                AND ( ot.s_paygate = 'MSP'
                      OR ot.s_paygate = 'QT' )
                AND p.s_psp_id = 'ONECREDIT'
                AND p.d_update > sysdate - INTERVAL '1' DAY
            UNION ALL
            SELECT
                i.s_id                                                                      s_invoice_id,
                p.s_id                                                                      s_key,
                i.d_update                                                                  d_update_invoice,
                ot.n_transaction_id                                                         n_order_txn_id,
                decode(p.s_psp_id, 'ONECOMM', 'ND')                                         s_trans_type,
                ''                                                                          s_app_name,
                decode(instr(p.s_data, '"id"'),
                       0,
                       to_char(tbl.n_transaction_id),
                       nvl(regexp_replace(p.s_data, '.*"id":(\d*),.*', '\1'),
                           to_char(tbl.n_transaction_id)))                                  s_transactionid,
                p.d_create                                                                  d_date,
                i.s_merchant_id                                                             s_merchantid,
                o.s_merchant_id                                                             AS s_invoice_merchantid,
                i.s_info                                                                    s_orderreference,
                to_char(o.n_ordered_id)                                                     s_orderid,
                p.s_client_merch_txn_ref                                                    s_merchanttransactionreferen,
                'Purchase'                                                                  s_transactiontype,
                ''                                                                          s_batchnumber,
                p.s_currency,
                ( p.n_amount * 100 )                                                        AS n_amount,
                ''                                                                          s_rrn,
                decode(p.s_state,
                       'approved',
                       '0',
                       invoice_get_response_code_nd(p.s_state,
                                                    to_char(tbl.n_card_verification_code))) s_response_code,
                ''                                                                          s_acquirerresponsecode,
                ''                                                                          s_operatorid,
                ''                                                                          s_merchanttransactionsource,
                ''                                                                          s_orderdate,
                p.s_ins_number                                                              s_cardno,
                ac.s_acquirer_name                                                          s_card_type,
                p.s_ins_month
                || '/'
                || p.s_ins_year                                                             s_card_exp,
                p.s_ins_name                                                                s_name_on_card,
                ''                                                                          s_fraud,
                ''                                                                          s_fraud_description,
                ''                                                                          s_frauddate,
                ''                                                                          s_authorised_amount,
                ''                                                                          s_captured_amount,
                ''                                                                          s_refunded_amount,
                ''                                                                          s_payment_method,
                ''                                                                          s_integration_type,
                ''                                                                          s_integration_type_version,
                ''                                                                          s_email_merch,
                ''                                                                          s_email_customer,
                ''                                                                          s_email_fraud,
                p.d_settlement,
                2                                                                           n_version,
                decode(p.s_psp_id, 'ONECOMM', 'ND')                                         s_paygate,
                ''                                                                          s_status,
                ''                                                                          s_reason_name,
                p.s_data                                                                    s_data,
                ''                                                                          s_void_desc,
                p.d_update                                                                  d_update,
                p.d_update                                                                  d_update1,
                o.s_customer_name,
                o.s_order_note,
                o.s_customer_email,
                o.s_user_id,
                to_char(ot.n_transaction_id)                                                s_org_merch_ref
            FROM
                     tb_invoice_order o
                INNER JOIN tb_invoice_order_trans ot ON ( o.n_ordered_id = ot.n_order_id
                                                          AND ot.s_paygate = 'MSP' )
                INNER JOIN tb_msp_invoice         i ON ( to_char(ot.n_transaction_id) = i.s_merch_invoice_ref
                                                 AND o.s_order_info = i.s_info )
                INNER JOIN tb_msp_payment         p ON p.s_invoice_id = i.s_id
                INNER JOIN tb_p2_transaction      tbl ON ( p.s_client_merch_txn_ref = tbl.s_merchant_transaction_ref
                                                      AND p.s_client_merchant_id = tbl.s_merchant_id )
                INNER JOIN tb_p2_acquirer         ac ON tbl.n_bank_id = ac.n_acquirer_id
            WHERE
                p.d_update > sysdate - INTERVAL '1' DAY
            UNION ALL
            SELECT
                NULL                                                              s_invoice_id,
                to_char(p.n_transaction_id)                                       s_key,
                NULL                                                              d_update_invoice,
                tbot.n_transaction_id                                             n_order_txn_id,
                'ND'                                                              s_trans_type,
                ''                                                                s_app_name,
                to_char(p.n_transaction_id)                                       s_transactionid,
                p.d_merchant_transaction_date                                     d_date,
                p.s_merchant_id                                                   s_merchantid,
                o.s_merchant_id                                                   AS s_invoice_merchantid,
                p.s_order_info                                                    s_orderreference,
                to_char(o.n_ordered_id)                                           s_orderid,
                p.s_merchant_transaction_ref                                      s_merchanttransactionreferen,
                'Purchase'                                                        s_transactiontype,
                ''                                                                s_batchnumber,
                p.s_currency_code,
                ( p.n_amount * 100 )                                              AS n_amount,
                ''                                                                s_rrn,
                invoice_get_response_code_nd(payment.s_state,
                                             to_char(p.n_card_verification_code)) s_response_code,
                ''                                                                s_acquirerresponsecode,
                ''                                                                s_operatorid,
                ''                                                                s_merchanttransactionsource,
                ''                                                                s_orderdate,
                p.s_card_number                                                   s_cardno,
                ac.s_acquirer_name                                                s_card_type,
                p.s_card_date                                                     s_card_exp,
                p.s_card_holder_name                                              s_name_on_card,
                ''                                                                s_fraud,
                ''                                                                s_fraud_description,
                ''                                                                s_frauddate,
                ''                                                                s_authorised_amount,
                ''                                                                s_captured_amount,
                ''                                                                s_refunded_amount,
                ''                                                                s_payment_method,
                ''                                                                s_integration_type,
                ''                                                                s_integration_type_version,
                ''                                                                s_email_merch,
                ''                                                                s_email_customer,
                ''                                                                s_email_fraud,
                p.d_merchant_transaction_date,
                2                                                                 n_version,
                'ND'                                                              s_paygate,
                ''                                                                s_status,
                ''                                                                s_reason_name,
                ''                                                                s_data,
                ''                                                                s_void_desc,
                p.d_update                                                        d_update,
                p.d_update                                                        d_update1,
                o.s_customer_name,
                o.s_order_note,
                o.s_customer_email,
                o.s_user_id,
                to_char(tbot.n_transaction_id)                                    s_org_merch_ref
            FROM
                     tb_invoice_order o
                INNER JOIN tb_invoice_order_trans tbot ON ( o.n_ordered_id = tbot.n_order_id
                                                            AND tbot.s_paygate = 'ND' )
                INNER JOIN tb_msp_invoice         i ON ( to_char(tbot.n_transaction_id) = i.s_merch_invoice_ref
                                                 AND o.s_order_info = i.s_info )
                INNER JOIN tb_msp_payment         payment ON payment.s_invoice_id = i.s_id
                INNER JOIN tb_p2_transaction      p ON ( payment.s_client_merch_txn_ref = p.s_merchant_transaction_ref
                                                    AND payment.s_client_merchant_id = p.s_merchant_id )
                INNER JOIN tb_p2_acquirer         ac ON p.n_bank_id = ac.n_acquirer_id
            WHERE
                p.d_update > sysdate - INTERVAL '1' DAY
        );