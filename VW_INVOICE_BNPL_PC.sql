-- ONEDATA.VW_INVOICE_BNPL_PC source

CREATE OR R<PERSON>LACE FORCE EDITIONABLE VIEW "ONEDATA"."VW_INVOICE_BNPL_PC" ("S_INVOICE_ID", "S_KEY", "D_UPDATE_INVOICE", "S_ORDER_TXN_ID", "S_TRANS_TYPE", "S_APP_NAME", "S_TRANSACTIONID", "D_DATE", "S_MERCHANTID", "S_INVOICE_MERCHANTID", "S_ORDERREFERENCE", "S_ORDERID", "S_ME<PERSON>HANT<PERSON>ANSACTIONREFEREN", "S_TRANSACTIONTYPE", "S_ACQUIRERID", "S_<PERSON>TCHNUMBER", "S_CURRENCY", "N_AMOUNT", "S_RRN", "S_RESPONSE_CODE", "S_ACQUIRERRESPONSECODE", "S_AUTHORIS<PERSON>ION_CODE", "S_<PERSON><PERSON><PERSON><PERSON>OR<PERSON>", "S_ME<PERSON>HANT<PERSON>ANSACTIONSOURCE", "S_OR<PERSON>RDAT<PERSON>", "S_CARD_TYPE", "S_<PERSON>RDN<PERSON>", "S_CARD_EXP", "S_CSCRESULT_CODE", "S_FRAUD", "S_FRAUD_DESCRIPTION", "S_FRAUDDATE", "S_TICKET_NUMBER", "S_AUTHORISED_AMOUNT", "S_CAPTURED_AMOUNT", "S_REFUNDED_AMOUNT", "S_ADDRESS", "S_CITY_TOWN", "S_STATE_PROVINCE", "S_ZIP_POSTAL_CODE", "S_COUNTRY", "S_AVS_RESULT_CODE", "S_PAYMENT_METHOD", "S_INTEGRATION_TYPE", "S_INTEGRATION_TYPE_VERSION", "S_PAYMENT_AUTHENTICATION_ID", "S_AUTHENTICATION_TYPE", "S_AUTHENTICATION_STATE", "S_VERIFICATION_SECURITY_LEVEL", "S_EMAIL_MERCH", "S_EMAIL_CUSTOMER", "S_EMAIL_FRAUD", "S_REMOTE_ADD", "S_LOCALE", "D_SETTLEMENT", "S_CARD_LEVEL_INDICATOR", "S_COMMERCIAL_CARD", "S_COMMERCIAL_CARD_INDICATOR", "S_RISK_OVERALL_RESULT", "S_TXN_REVERSAL_RESULT", "N_VERSION", "S_PAYGATE", "S_STATUS", "S_REASON_NAME", "S_DATA", "S_VOID_DESC", "D_UPDATE", "D_UPDATE2", "S_CUSTOMER_NAME", "S_ORDER_NOTE", "S_CUSTOMER_EMAIL", "S_USER_ID") DEFAULT COLLATION "USING_NLS_COMP"  AS 
  SELECT
        "S_INVOICE_ID",
        "S_KEY",
        "D_UPDATE_INVOICE",
        "S_ORDER_TXN_ID",
        "S_TRANS_TYPE",
        "S_APP_NAME",
        "S_TRANSACTIONID",
        "D_DATE",
        "S_MERCHANTID",
        "S_INVOICE_MERCHANTID",
        "S_ORDERREFERENCE",
        "S_ORDERID",
        "S_MERCHANTTRANSACTIONREFEREN",
        "S_TRANSACTIONTYPE",
        "S_ACQUIRERID",
        "S_BATCHNUMBER",
        "S_CURRENCY",
        "N_AMOUNT",
        "S_RRN",
        "S_RESPONSE_CODE",
        "S_ACQUIRERRESPONSECODE",
        "S_AUTHORISATION_CODE",
        "S_OPERATORID",
        "S_MERCHANTTRANSACTIONSOURCE",
        "S_ORDERDATE",
        "S_CARD_TYPE",
        "S_CARDNO",
        "S_CARD_EXP",
        "S_CSCRESULT_CODE",
        "S_FRAUD",
        "S_FRAUD_DESCRIPTION",
        "S_FRAUDDATE",
        "S_TICKET_NUMBER",
        "S_AUTHORISED_AMOUNT",
        "S_CAPTURED_AMOUNT",
        "S_REFUNDED_AMOUNT",
        "S_ADDRESS",
        "S_CITY_TOWN",
        "S_STATE_PROVINCE",
        "S_ZIP_POSTAL_CODE",
        "S_COUNTRY",
        "S_AVS_RESULT_CODE",
        "S_PAYMENT_METHOD",
        "S_INTEGRATION_TYPE",
        "S_INTEGRATION_TYPE_VERSION",
        "S_PAYMENT_AUTHENTICATION_ID",
        "S_AUTHENTICATION_TYPE",
        "S_AUTHENTICATION_STATE",
        "S_VERIFICATION_SECURITY_LEVEL",
        "S_EMAIL_MERCH",
        "S_EMAIL_CUSTOMER",
        "S_EMAIL_FRAUD",
        "S_REMOTE_ADD",
        "S_LOCALE",
        "D_SETTLEMENT",
        "S_CARD_LEVEL_INDICATOR",
        "S_COMMERCIAL_CARD",
        "S_COMMERCIAL_CARD_INDICATOR",
        "S_RISK_OVERALL_RESULT",
        "S_TXN_REVERSAL_RESULT",
        "N_VERSION",
        "S_PAYGATE",
        "S_STATUS",
        "S_REASON_NAME",
        "S_DATA",
        "S_VOID_DESC",
        "D_UPDATE",
        "D_UPDATE2",
        "S_CUSTOMER_NAME",
        "S_ORDER_NOTE",
        "S_CUSTOMER_EMAIL",
        "S_USER_ID"
    FROM
        (
            SELECT
                i.s_id                                                       s_invoice_id,
                tbl.s_id                                                     s_key,
                i.d_update                                                   d_update_invoice,
                to_char(ot.n_transaction_id)                                 s_order_txn_id,
                'BNPL'                                                       s_trans_type,
                invoice_convert_msp_app(tbl.s_client_id, tbl.s_ins_brand_id) s_app_name,
                tbl.s_id                                                     s_transactionid,
                tbl.d_create                                                 d_date,
                i.s_merchant_id                                              s_merchantid,
                orders.s_merchant_id                                         AS s_invoice_merchantid,
                i.s_info                                                     s_orderreference,
                to_char(orders.n_ordered_id)                                 s_orderid,
                i.s_merch_invoice_ref                                        s_merchanttransactionreferen,
                'Purchase'                                                   s_transactiontype,
                tbl.s_ins_brand_id                                           s_acquirerid,
                ''                                                           s_batchnumber,
                i.s_currencies                                               s_currency,
                ( tbl.n_amount * 100 )                                       AS n_amount,
                ''                                                           s_rrn,
                decode(tbl.s_state, 'approved', 'SUCCESS', 'pending', 'PENDING',
                       'FAILED')                                             s_response_code,
                ''                                                           s_acquirerresponsecode,
                ''                                                           s_authorisation_code,
                ''                                                           s_operatorid,
                ''                                                           s_merchanttransactionsource,
                ''                                                           s_orderdate,
                tbl.s_psp_id                                                 s_card_type,
                tbl.s_ins_number                                             s_cardno,
                tbl.s_ins_month
                || '/'
                || tbl.s_ins_year                                            s_card_exp,
                ''                                                           s_cscresult_code,
                ''                                                           s_fraud,
                ''                                                           s_fraud_description,
                ''                                                           s_frauddate,
                ''                                                           s_ticket_number,
                ''                                                           s_authorised_amount,
                ''                                                           s_captured_amount,
                ''                                                           s_refunded_amount,
                i.s_avs_street                                               s_address,
                i.s_avs_city                                                 s_city_town,
                i.s_avs_state_province                                       s_state_province,
                i.s_avs_postcode_zip                                         s_zip_postal_code,
                i.s_avs_country                                              s_country,
                ''                                                           s_avs_result_code,
                ''                                                           s_payment_method,
                ''                                                           s_integration_type,
                ''                                                           s_integration_type_version,
                ''                                                           s_payment_authentication_id,
                ''                                                           s_authentication_type,
                ''                                                           s_authentication_state,
                ''                                                           s_verification_security_level,
                ''                                                           s_email_merch,
                ''                                                           s_email_customer,
                ''                                                           s_email_fraud,
                ''                                                           s_remote_add,
                ''                                                           s_locale,
                tbl.d_settlement,
                ''                                                           s_card_level_indicator,
                ''                                                           s_commercial_card,
                ''                                                           s_commercial_card_indicator,
                ''                                                           s_risk_overall_result,
                ''                                                           s_txn_reversal_result,
                2                                                            n_version,
                'BNPL'                                                       s_paygate,
                CASE
                    WHEN tbl.s_ins_brand_id = 'amigo' THEN
                            CASE
                                WHEN i.s_state <> 'expired'
                                     AND tbl.s_state = 'authorization_required' THEN
                                    'authorization_required'
                                WHEN i.s_state <> 'expired'
                                     AND tbl.s_state = 'pending' THEN
                                    'verified'
                                WHEN i.s_state = 'paid'
                                     AND ( tbl.s_state = 'approved'
                                           OR tbl.s_state = 'paid' ) THEN
                                    'approved'
                                WHEN i.s_state = 'expired'
                                     AND tbl.s_state = 'authorization_required' THEN
                                    'failed'
                                WHEN i.s_state = 'expired'
                                     AND tbl.s_state IN ( 'failed', 'pending', 'expired' ) THEN
                                    'failed'
                                WHEN i.s_state = 'closed'
                                     AND tbl.s_state = 'failed' THEN
                                    'failed'
                                WHEN i.s_state = 'not_paid'
                                     AND tbl.s_state = 'failed' THEN
                                    'decline'
                                ELSE
                                    'failed'
                            END
                    WHEN tbl.s_ins_brand_id = 'kredivo'
                         OR tbl.s_ins_brand_id = 'kbank'
                         OR tbl.s_ins_brand_id = 'homecredit' THEN
                            CASE
                                WHEN i.s_state = 'not_paid'
                                     AND tbl.s_state = 'authorization_required' THEN
                                    'authorization_required'
                                WHEN i.s_state = 'paid'
                                     AND tbl.s_state = 'approved' THEN
                                    'approved'
                                ELSE
                                    'failed'
                            END
                END                                                          AS s_status,
                CASE
                    WHEN i.s_state <> 'expired'
                         AND tbl.s_state = 'pending'
                         AND tbl.s_ins_brand_id = 'amigo' THEN
                        'AMIGO_APPROVED'
                    WHEN i.s_state = 'paid'
                         AND tbl.s_state = 'approved'
                         AND tbl.s_ins_brand_id = 'amigo' THEN
                        'AMIGO_APPROVED'
                    ELSE
                        tbl.s_reason_name
                END                                                          AS s_reason_name,
                tbl.s_data,
                ''                                                           s_void_desc,
                tbl.d_update,
                tbl.d_update                                                 d_update2,
                orders.s_customer_name,
                orders.s_order_note,
                orders.s_customer_email,
                orders.s_user_id
            FROM
                     tb_invoice_order orders
                INNER JOIN tb_invoice_order_trans ot ON orders.n_ordered_id = ot.n_order_id
                INNER JOIN tb_msp_invoice         i ON ( to_char(ot.n_transaction_id) = i.s_merch_invoice_ref
                                                 AND ot.s_merchant_id = i.s_merchant_id
                                                 AND upper(ot.s_paygate) IN ( 'BNPL', 'MSP' ) )
                INNER JOIN tb_msp_payment         tbl ON tbl.s_invoice_id = i.s_id
            WHERE
                    tbl.s_ins_type = 'bnpl'
                AND tbl.d_update > sysdate - INTERVAL '1' DAY
        );