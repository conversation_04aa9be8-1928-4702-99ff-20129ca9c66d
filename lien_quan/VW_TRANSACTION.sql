-- PAYMENT2.VW_TRANSACTION source

CREATE OR REPLACE FORCE VIEW "PAYMENT2"."VW_TRANSACTION" ("N_TRANSACTION_ID", "S_VPC_VERSION", "S_MERCHANT_ID", "D_MERCHANT_TRANSACTION_DATE", "S_MERCHANT_TRANSACTION_REF", "S_COMMAND_TYPE", "S_ORDER_INFO", "N_AMOUNT", "S_CURRENCY_CODE", "S_TRANSATION_LANGUAGE", "S_CARD_NUMBER", "S_CARD_HOLDER_NAME", "S_CARD_DATE", "N_CARD_VERIFICATION_RETRY", "N_CARD_VERIFICATION_CODE", "S_CARD_VERIFICATION_INFO", "D_CARD_VERIFICATION_DATE", "S_USER_AUTHENTICATION_URL", "N_USER_AUTHENTICATION_CODE", "S_USER_AUTHENTICATION_INFO", "D_USER_AUTHENTICATION_DATE", "S_RETURN_AUTHENTICATION_URL", "S_TRANSACTION_INFO", "N_TRANSACTION_STATUS", "S_IP", "N_REFUND_AMOUNT", "N_DO_REFUND", "N_REFUND_COUNT", "S_RETURN_URL", "N_BANK_ID", "N_CARD_AMOUNT", "N_LAST_PAYMENT_ID", "D_UPDATE", "S_CONTRACT_RELATION", "S_BANK_MERCHANT_ID") AS 
  SELECT
        t."N_TRANSACTION_ID",
        t."S_VPC_VERSION",
        t."S_MERCHANT_ID",
        t."D_MERCHANT_TRANSACTION_DATE",
        t."S_MERCHANT_TRANSACTION_REF",
        t."S_COMMAND_TYPE",
        t."S_ORDER_INFO",
        t."N_AMOUNT",
        t."S_CURRENCY_CODE",
        t."S_TRANSATION_LANGUAGE",
        t."S_CARD_NUMBER",
        t."S_CARD_HOLDER_NAME",
        t."S_CARD_DATE",
        t."N_CARD_VERIFICATION_RETRY",
        t."N_CARD_VERIFICATION_CODE",
        t."S_CARD_VERIFICATION_INFO",
        t."D_CARD_VERIFICATION_DATE",
        t."S_USER_AUTHENTICATION_URL",
        t."N_USER_AUTHENTICATION_CODE",
        t."S_USER_AUTHENTICATION_INFO",
        t."D_USER_AUTHENTICATION_DATE",
        t."S_RETURN_AUTHENTICATION_URL",
        t."S_TRANSACTION_INFO",
        t."N_TRANSACTION_STATUS",
        t."S_IP",
        t."N_REFUND_AMOUNT",
        t."N_DO_REFUND",
        t."N_REFUND_COUNT",
        t."S_RETURN_URL",
        t."N_BANK_ID",
        null N_CARD_AMOUNT,
        t."N_LAST_PAYMENT_ID",
        t."D_UPDATE",
--        get_contract_relation(t.n_bank_id, t.s_merchant_id, t.n_transaction_id) AS s_contract_relation,
        decode(regexp_replace(tef.s_data,'.+\"contract_type":\"([^\"]+).+','\1'),'2B','2B','3B','3B','4B','4B',get_contract_relation(t.n_bank_id, t.s_merchant_id, t.n_transaction_id)) AS s_contract_relation,
        tef.s_bank_merchant_id
    FROM
             transaction t
        JOIN tb_txn_ext_fields tef ON t.n_transaction_id = tef.n_transaction_id
    WHERE
        t.d_update >= trunc(sysdate - 50)
 ;

GRANT UPDATE ON "PAYMENT2"."VW_TRANSACTION" TO "OP_PAYMENT2_EDIT";
  GRANT INSERT ON "PAYMENT2"."VW_TRANSACTION" TO "OP_PAYMENT2_EDIT";
  GRANT DELETE ON "PAYMENT2"."VW_TRANSACTION" TO "OP_PAYMENT2_EDIT";
  GRANT SELECT ON "PAYMENT2"."VW_TRANSACTION" TO "OP_PAYMENT2_SELECT";